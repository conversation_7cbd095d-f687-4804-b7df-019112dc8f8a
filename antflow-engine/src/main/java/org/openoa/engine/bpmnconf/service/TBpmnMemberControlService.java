package org.openoa.engine.bpmnconf.service;

import org.openoa.engine.bpmnconf.confentity.TBpmnMemberControl;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_bpmn_member_control(审批人-关联成员控件记录表)】的数据库操作Service
* @createDate 2025-04-27 14:21:46
*/
public interface TBpmnMemberControlService extends IService<TBpmnMemberControl> {

    /**
     * 根据表单code获取关联成员控件id
     * @param formCode
     * @return
     */
    List<TBpmnMemberControl> getBpmnMemberControlInfo(String formCode);
}
