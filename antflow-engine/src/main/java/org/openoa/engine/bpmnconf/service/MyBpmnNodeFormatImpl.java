package org.openoa.engine.bpmnconf.service;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.ButtonTypeEnum;
import org.openoa.base.constant.enums.NodePropertyEnum;
import org.openoa.base.constant.enums.NodeTypeEnum;
import org.openoa.base.constant.enums.ProcessNodeEnum;
import org.openoa.base.exception.JiMuBizException;
import org.openoa.base.util.BpmnUtils;
import org.openoa.base.util.FilterUtil;
import org.openoa.base.util.SpringBeanUtils;
import org.openoa.base.vo.*;
import org.openoa.common.util.BpmnElementUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static org.openoa.base.constant.enums.ElementTypeEnum.ELEMENT_TYPE_END_EVENT;
import static org.openoa.base.constant.enums.ElementTypeEnum.ELEMENT_TYPE_START_EVENT;
import static org.openoa.base.constant.enums.NodeTypeEnum.NODE_TYPE_START;
import static org.openoa.base.constant.enums.ProcessNodeEnum.START_TASK_KEY;

@Slf4j
@Component
public class MyBpmnNodeFormatImpl {


    public List<BpmnConfCommonElementVo> getBpmnConfCommonElementVoList(BpmnConfCommonVo bpmnConfCommonVo,
                                                                        List<BpmnNodeVo> nodes, BpmnStartConditionsVo bpmnStartConditions) {

        if (ObjectUtils.isEmpty(nodes)) {
            return Collections.EMPTY_LIST;
        }

        Integer sequenceFlowNum = 1;//start sequence flow number

        String startElementId = START_TASK_KEY.getDesc();//start element


        //process diagram element list
        List<BpmnConfCommonElementVo> bpmnConfCommonElementVos = Lists.newArrayList();

        //set start element
        String startEventElementId = StringUtils.join(bpmnConfCommonVo.getProcessNum(), "_", ELEMENT_TYPE_START_EVENT.getDesc());
        BpmnConfCommonElementVo startEventElement = BpmnElementUtils.getStartEventElement(startEventElementId);
        bpmnConfCommonElementVos.add(startEventElement);

        //rebuild nodes recursively
        List<BpmnNodeVo> rebuildNodesList = Lists.newArrayList();
        BpmnNodeVo startNode = getStartUserNode(nodes);
        rebuildNodesList.add(startNode);//set start node
        //rebuildNodes(rebuildNodesList, nodes, startNode);
	    // 此时rebuildNodesList只有一个发起人节点，经过下面的处理，会把nodes里的节点（除了抄送人外）都添加到rebuildNodesList里
        treatNodesRecursively(rebuildNodesList,nodes,startNode);

        //set start user node,you may see that start and end node are two special nodes and should be handled separately
        BpmnNodeVo startUserNode = getStartUserNode(rebuildNodesList);
        String startUserId = Optional.ofNullable(bpmnStartConditions.getStartUserId())
                .map(Objects::toString)
                .orElse("");
        Map<String,String> singleAssigneeMap=new HashMap<>();
        singleAssigneeMap.put(startUserId,bpmnStartConditions.getStartUserName());
        BpmnConfCommonElementVo startNodeElement = BpmnElementUtils.getSingleElement(startElementId, "发起人", "startUser",startUserId,singleAssigneeMap);


        //set start user node buttons
        setStartNodeElementButtons(startUserNode, startNodeElement);

        //set start user node template
        startNodeElement.setTemplateVos(startUserNode.getTemplateVos());


        //set start user approvement reminder
        startNodeElement.setApproveRemindVo(startUserNode.getApproveRemindVo());


        //add start node to elements list
        bpmnConfCommonElementVos.add(startNodeElement);


        //set start user node sequence flow
        bpmnConfCommonElementVos.add(BpmnElementUtils.getSequenceFlow(sequenceFlowNum,
                startEventElement.getElementId(), startNodeElement.getElementId()));


        //format BpmnNodeVo to bpmnConfCommonElementVo list recursively
        Integer nodeCode = ProcessNodeEnum.getCodeByDesc(startElementId);//the start node to begin to treat recursively

        HashMap<String, Integer> numMap = Maps.newHashMap();
        numMap.put("nodeCode", nodeCode);
        numMap.put("sequenceFlowNum", sequenceFlowNum);
        String startUserNodeTo = getNodeTo(startUserNode);//start user node to target node
	    // 修改点： 入参多传一个ccNodeList，用来查找不到nextNode节点的时候去抄送人节点里面查找
	    List<BpmnNodeVo> ccNodeList = nodes.stream().filter(x -> NodeTypeEnum.NODE_TYPE_COPY.getCode().equals(x.getNodeType())).collect(Collectors.toList());
	    //to judge whether the target node of the start user node is empty, if it is empty, no need to continue to assemble the node element
        if (!ObjectUtils.isEmpty(startUserNodeTo)) {
            formatNodes(bpmnConfCommonElementVos,rebuildNodesList,startUserNodeTo,nodeCode,sequenceFlowNum,numMap,ccNodeList);
            //formatNodesToElements(bpmnConfCommonElementVos, rebuildNodesList, startUserNodeTo, nodeCode, sequenceFlowNum, numMap);
        }

        //the last node's id
        String lastNodeId = ProcessNodeEnum.getDescByCode(numMap.get("nodeCode"));

        //last sequence flow number
        Integer lastSequenceFlowNum = numMap.get("sequenceFlowNum") + 1;

        //set end element
        String endEventElementId = StringUtils.join(bpmnConfCommonVo.getProcessNum(), "_", ELEMENT_TYPE_END_EVENT.getDesc());
        BpmnConfCommonElementVo endEventElement = BpmnElementUtils.getEndEventElement(endEventElementId);
        bpmnConfCommonElementVos.add(endEventElement);


        // set last node to end node sequence flow
        BpmnConfCommonElementVo lastSequenceFlow = BpmnElementUtils
                .getSequenceFlow(lastSequenceFlowNum, lastNodeId, endEventElement.getElementId());
        lastSequenceFlow.setIsLastSequenceFlow(1);
        bpmnConfCommonElementVos.add(lastSequenceFlow);

        return bpmnConfCommonElementVos;
    }

    /**
     * set start node element buttons
     *
     * @param startUserNode
     * @param startNodeElement
     */
    private void setStartNodeElementButtons(BpmnNodeVo startUserNode, BpmnConfCommonElementVo startNodeElement) {
        startNodeElement.setButtons(BpmnConfCommonButtonsVo
                .builder()
                .startPage(startUserNode.getButtons().getStartPage()
                        .stream()
                        .map(o -> {
                            return BpmnConfCommonButtonPropertyVo
                                    .builder()
                                    .buttonType(o)
                                    .buttonName(ButtonTypeEnum.getDescByCode(o))
                                    .build();
                        })
                        .collect(Collectors.toList()))
                .approvalPage(startUserNode.getButtons().getApprovalPage()
                        .stream()
                        .map(o -> BpmnConfCommonButtonPropertyVo
                                .builder()
                                .buttonType(o)
                                .buttonName(ButtonTypeEnum.getDescByCode(o))
                                .build())
                        .collect(Collectors.toList()))
                .build());
    }

    /**
     * get start user node
     *
     * @param nodes
     * @return
     */
    private BpmnNodeVo getStartUserNode(List<BpmnNodeVo> nodes) {
        List<BpmnNodeVo> startNode = nodes
                .stream()
                .filter(o -> o.getNodeType().equals(NODE_TYPE_START.getCode()))
                .collect(Collectors.toList());

        if (ObjectUtils.isEmpty(startNode)) {
            throw new JiMuBizException("未找到开始节点流程发起失败");
        }
        return startNode.get(0);
    }

    /**
     * rebuild nodes recursively
     *
     * @param rebuildNodesList
     * @param nodes
     * @param nodeVo
     */
    private void rebuildNodes(List<BpmnNodeVo> rebuildNodesList, List<BpmnNodeVo> nodes, BpmnNodeVo nodeVo) {


        BpmnNodeParamsVo nodeParamsVo = nodeVo.getParams();

        if (nodeParamsVo.getIsNodeDeduplication() == 1) {

            //skip deduplicated node and rebuild nodeTo
            String nextNodeTo = getNodeTo(nodeVo);
            List<BpmnNodeVo> nodeVos = rebuildNodesList.stream().filter(a -> a.getParams().getNodeTo().equals(nodeVo.getNodeId())).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(nodeVos)){
                BpmnNodeVo vo = nodeVos.get(0);
                    if(NodeTypeEnum.NODE_TYPE_PARALLEL_GATEWAY.getCode().equals(vo.getNodeType())){
                        BpmnNodeVo aggregationNode = BpmnUtils.getAggregationNode(vo, nodes);
                        if(aggregationNode==null){
                            throw new JiMuBizException("there is a parallel gateway node,but can not get its aggregation node");
                        }
                        if(aggregationNode.getNodeId().equals(nextNodeTo)){
                            nodeVo.getParams().setIsNodeDeduplication(0);
                            nodeVo.setDeduplicationExclude(true);
                            rebuildNodesList.add(nodeVo);
                            return;
                        }
                    }
                    vo.getParams().setNodeTo(nextNodeTo);
                    List<String> nodeTo = vo.getNodeTo();
                    List<String> newNodeTos=new ArrayList<>();
                    for (String s : nodeTo) {
                        if(s.equals(nodeVo.getNodeId())){
                            newNodeTos.add(nextNodeTo);
                        }else{
                            newNodeTos.add(s);
                        }
                    }
                    vo.setNodeTo(newNodeTos);
            }
            //rebuildNodes(rebuildNodesList, nodes, nodeVo);
        } else {
            rebuildNodesList.add(nodeVo);
           /* if (!Strings.isNullOrEmpty(getNodeTo(nextNodeVo))) {
                rebuildNodes(rebuildNodesList, nodes, nextNodeVo);
            }*/
        }
    }
    private void treatNodesRecursively(List<BpmnNodeVo> rebuildNodesList, List<BpmnNodeVo> nodes, BpmnNodeVo nodeVo) {
        Map<String, BpmnNodeVo> mapNodes = nodes.stream().collect(Collectors.toMap(BpmnNodeVo::getNodeId, a->a,(k1, k2)->k1));
        String nextId=null;
        do {
            String nodeTo = getNodeTo(nodeVo);

            if (Strings.isNullOrEmpty(nodeTo)) {
                return;
            }
	        List<BpmnNodeVo> ccNodeList = nodes.stream().filter(x -> x.getNodeType().equals(NodeTypeEnum.NODE_TYPE_COPY.getCode())).collect(Collectors.toList());
	        BpmnNodeVo nextNodeVo = getNextNodeVo(nodes, nodeTo , ccNodeList);
            BpmnNodeParamsVo nextParams = nextNodeVo.getParams();
	        if(nextParams==null||StringUtils.isEmpty(nextParams.getNodeTo())&&NodeTypeEnum.NODE_TYPE_COPY.getCode().equals(nextNodeVo.getNodeType())){
               return;
            }
            if(NodeTypeEnum.NODE_TYPE_PARALLEL_GATEWAY.getCode().equals(nextNodeVo.getNodeType())){
                BpmnNodeVo aggregationNode = BpmnUtils.getAggregationNode(nextNodeVo, mapNodes.values());
                treatParallelGateWayRecursively(nextNodeVo,aggregationNode,mapNodes,nodes,rebuildNodesList, ccNodeList);
                nodeVo=aggregationNode;
            }else{
                rebuildNodes(rebuildNodesList,nodes,nextNodeVo);
                nodeVo=nextNodeVo;
            }
            nextId=nodeVo.getParams().getNodeTo();
        }while (!StringUtils.isEmpty(nextId));

    }

    private void treatParallelGateWayRecursively(BpmnNodeVo outerMostParallelGatewayNode,BpmnNodeVo itsAggregationNode,Map<String, BpmnNodeVo> mapNodes,List<BpmnNodeVo> nodes,List<BpmnNodeVo> rebuildNodesList, List<BpmnNodeVo> ccNodeList){
        if (itsAggregationNode == null) {
            throw new JiMuBizException("there is a parallel gateway node,but can not get its aggregation node!");
        }
        String aggregationNodeNodeId = itsAggregationNode.getNodeId();
        List<String> nodeTos = outerMostParallelGatewayNode.getNodeTo();
        rebuildNodes(rebuildNodesList,nodes,outerMostParallelGatewayNode);
        rebuildNodes(rebuildNodesList,nodes,itsAggregationNode);
        for (String nodeTo : nodeTos) {
            BpmnNodeVo currentNodeVo = mapNodes.get(nodeTo);
            //treat all nodes between parallel gateway and its aggregation node(not include the latter)
            for (BpmnNodeVo nodeVo = currentNodeVo; nodeVo!=null&&!nodeVo.getNodeId().equals(aggregationNodeNodeId); nodeVo = mapNodes.get(nodeVo.getParams().getNodeTo())) {
                if (NodeTypeEnum.NODE_TYPE_PARALLEL_GATEWAY.getCode().equals(nodeVo.getNodeType())) {
                    BpmnNodeVo aggregationNode = BpmnUtils.getAggregationNode(nodeVo, mapNodes.values());
                    treatParallelGateWayRecursively(nodeVo, aggregationNode, mapNodes,nodes,rebuildNodesList, ccNodeList);
                }else{
                    rebuildNodes(rebuildNodesList,nodes,nodeVo);
                }
            }
        }
    }

    private Map<String, Integer> formatNodes(List<BpmnConfCommonElementVo> bpmnConfCommonElementVos,
                                             List<BpmnNodeVo> rebuildNodesList, String nodeTo,
                                             Integer nodeCode, Integer sequenceFlowNum, HashMap<String, Integer> numMap, List<BpmnNodeVo> ccNodeList){

        rebuildNodesList=rebuildNodesList.stream().filter(FilterUtil.distinctByKeys(BpmnNodeVo::getNodeId)).collect(Collectors.toList());
        Map<String, BpmnNodeVo> mapNodes = rebuildNodesList.stream().collect(Collectors.toMap(BpmnNodeVo::getNodeId, a->a,(k1, k2)->k1));
        BpmnNodeVo aggregationNode=null;
        do {
            BpmnNodeVo nextNodeVo = getNextNodeVo(rebuildNodesList, nodeTo, ccNodeList);
            if(NodeTypeEnum.NODE_TYPE_PARALLEL_GATEWAY.getCode().equals(nextNodeVo.getNodeType())){
                aggregationNode= BpmnUtils.getAggregationNode(nextNodeVo, mapNodes.values());
                Integer parallelGatWayNodeCode = numMap.get("nodeCode")+1;
                Integer parallelGateWaySequenceFlowNum=numMap.get("sequenceFlowNum")+1;
                numMap.put("nodeCode",parallelGatWayNodeCode);
                numMap.put("sequenceFlowNum",parallelGateWaySequenceFlowNum);
                //if node is of type parallel gateway,then generate parallel gateway element and add it to element list
                BpmnConfCommonElementVo parallelGateWayElement = BpmnElementUtils.getParallelGateWayElement(parallelGateWaySequenceFlowNum);
                bpmnConfCommonElementVos.add(parallelGateWayElement);
                //add sequence flow from prev element to gateway node
                bpmnConfCommonElementVos.add(BpmnElementUtils.getSequenceFlow(parallelGateWaySequenceFlowNum,
                        ProcessNodeEnum.getDescByCode( parallelGatWayNodeCode-1), parallelGateWayElement.getElementId()));
                treatParallelGateWayRecursively(nextNodeVo
                        ,aggregationNode
                        ,mapNodes
                        ,bpmnConfCommonElementVos
                        ,rebuildNodesList
                        ,nextNodeVo.getNodeId()
                        ,parallelGatWayNodeCode
                        ,parallelGateWaySequenceFlowNum,
                        numMap,new BpmnNodeVo(),new HashSet<>(), ccNodeList);
                nodeTo=aggregationNode.getNodeId();
            }else{
                //if it is aggregation node,then to generate an aggregation parallel gateway node,and set nodefroms to flow to it,then set it flow to current node
                //note that aggregationNode is common user task node,if more than one node flow to it,then generate an aggregation gateway node and set the generated gateway node flow to it
                if(nextNodeVo.equals(aggregationNode)){
                    List<BpmnNodeVo> nodeFroms = getNodeFroms(rebuildNodesList,nextNodeVo);
                    nextNodeVo.setFromNodes(nodeFroms);
                }
	            formatNodesToElements(bpmnConfCommonElementVos,rebuildNodesList,nextNodeVo.getNodeId(),numMap.get("nodeCode"),numMap.get("sequenceFlowNum"),numMap, ccNodeList);
                nodeTo=nextNodeVo.getParams().getNodeTo();
            }
        }while (!StringUtils.isEmpty(nodeTo));
	    // rebuildNodesList.removeIf(o -> NodeTypeEnum.NODE_TYPE_COPY.getCode().equals(o.getNodeType()));
        return numMap;
    }

    private void treatParallelGateWayRecursively(BpmnNodeVo outerMostParallelGatewayNode
            ,BpmnNodeVo itsAggregationNode,
                                                 Map<String, BpmnNodeVo> mapNodes,
                                                 List<BpmnConfCommonElementVo> bpmnConfCommonElementVos,
                                                 List<BpmnNodeVo> rebuildNodesList,String nextNodeTo,
                                                 Integer nodeCode, Integer sequenceFlowNum, HashMap<String, Integer> numMap,
                                                 BpmnNodeVo lastAggNode,Set<String> alreadyProcessNodeIds, List<BpmnNodeVo> ccNodeList){
        if (itsAggregationNode == null) {
            throw new JiMuBizException("there is a parallel gateway node,but can not get its aggregation node!");
        }
        String aggregationNodeNodeId = itsAggregationNode.getNodeId();
        List<String> nodeTos = outerMostParallelGatewayNode.getNodeTo();
        String parallelGatewayNodeNodeId = outerMostParallelGatewayNode.getNodeId();
        Integer gateWaySF = numMap.get("sequenceFlowNum");
        for (String nodeTo : nodeTos) {
            BpmnNodeVo currentNodeVo = mapNodes.get(nodeTo);
            bpmnConfCommonElementVos.add(BpmnElementUtils.getSequenceFlow(numMap.get("sequenceFlowNum")+1,
                    "gateWay"+gateWaySF,  ProcessNodeEnum.getDescByCode(numMap.get("nodeCode")+1)));
            //treat all nodes between parallel gateway and its aggregation node(not include the latter)
            for (BpmnNodeVo nodeVo = currentNodeVo; nodeVo!=null&&!nodeVo.getNodeId().equals(aggregationNodeNodeId); nodeVo = mapNodes.get(nodeVo.getParams().getNodeTo())) {
                if(alreadyProcessNodeIds.contains(nodeVo.getNodeId())){
                    continue;
                }
                if (NodeTypeEnum.NODE_TYPE_PARALLEL_GATEWAY.getCode().equals(nodeVo.getNodeType())) {
                    alreadyProcessNodeIds.add(nodeVo.getNodeId());
                    BpmnNodeVo aggregationNode = BpmnUtils.getAggregationNode(nodeVo, mapNodes.values());
                    lastAggNode=aggregationNode;
                    Integer parallelGatWayNodeCode =numMap.get("nodeCode")+1;
                    Integer parallelGateWaySequenceFlowNum=numMap.get("sequenceFlowNum")+1;
                    numMap.put("nodeCode",parallelGatWayNodeCode);
                    numMap.put("sequenceFlowNum",parallelGateWaySequenceFlowNum);
                    BpmnConfCommonElementVo parallelGateWayElement = BpmnElementUtils.getParallelGateWayElement(parallelGateWaySequenceFlowNum);
                    bpmnConfCommonElementVos.add(parallelGateWayElement);
                    bpmnConfCommonElementVos.add(BpmnElementUtils.getSequenceFlow(parallelGateWaySequenceFlowNum,
                            ProcessNodeEnum.getDescByCode(parallelGatWayNodeCode-1), parallelGateWayElement.getElementId()));
                    treatParallelGateWayRecursively(nodeVo, aggregationNode, mapNodes,bpmnConfCommonElementVos,rebuildNodesList,nodeVo.getNodeId()
                            ,numMap.get("nodeCode"),numMap.get("sequenceFlowNum"),numMap,aggregationNode,alreadyProcessNodeIds, ccNodeList);
                }else{
                    alreadyProcessNodeIds.add(nodeVo.getNodeId());
                    if(nodeVo.equals(itsAggregationNode)){
                        List<BpmnNodeVo> nodeFroms = getNodeFroms(rebuildNodesList,nodeVo);
                        nodeVo.setFromNodes(nodeFroms);
                    }
                    formatNodesToElements(bpmnConfCommonElementVos,rebuildNodesList,nodeVo.getNodeId(),numMap.get("nodeCode"),numMap.get("sequenceFlowNum"),numMap, ccNodeList);
                }

            }
        }
        if(!StringUtils.isEmpty(lastAggNode.getNodeId())){
            if(alreadyProcessNodeIds.contains(lastAggNode.getNodeId())){
                return;
            }
            alreadyProcessNodeIds.add(lastAggNode.getNodeId());
            List<BpmnNodeVo> nodeFroms = getNodeFroms(rebuildNodesList,lastAggNode);
            lastAggNode.setFromNodes(nodeFroms);
            formatNodesToElements(bpmnConfCommonElementVos,rebuildNodesList,lastAggNode.getNodeId(),numMap.get("nodeCode"),numMap.get("sequenceFlowNum"),numMap, ccNodeList);
        }
    }
    /**
     * format bpmnNodeVo to BpmnConfCommonElementVo recursively
     *
     * @param bpmnConfCommonElementVos
     * @param rebuildNodesList
     * @param nodeTo
     */
    private Map<String, Integer> formatNodesToElements(List<BpmnConfCommonElementVo> bpmnConfCommonElementVos,
                                                       List<BpmnNodeVo> rebuildNodesList, String nodeTo,
                                                       Integer nodeCode, Integer sequenceFlowNum, HashMap<String, Integer> numMap, List<BpmnNodeVo> ccNodeList) {


	    //get next node according to nodeTo
	    BpmnNodeVo nextNodeVo = getNextNodeVo(rebuildNodesList, nodeTo, ccNodeList);


	    // use node property to get the adaptor and then format nodeVo to elementVo
	    NodePropertyEnum nodePropertyEnum = NodePropertyEnum.getNodePropertyEnumByCode(nextNodeVo.getNodeProperty());

	    MyBpmnElementAdaptor bean=null;
	    Collection<MyBpmnElementAdaptor> beans = SpringBeanUtils.getBeans(MyBpmnElementAdaptor.class);
	    for (MyBpmnElementAdaptor bpmnElementAdaptor : beans) {
		    if(bpmnElementAdaptor.isSupportBusinessObject(nodePropertyEnum)){
			    bean=bpmnElementAdaptor;
			    break;
		    }
	    }

	    if (!ObjectUtils.isEmpty(bean)) {
		    bean.doFormatNodesToElements(bpmnConfCommonElementVos, nextNodeVo, nodeCode, sequenceFlowNum, numMap);
	    }


	    //if there still has next node,then continue format
	    String nextNodeTo = getNodeTo(nextNodeVo);
        /*if (!ObjectUtils.isEmpty(nextNodeTo)) {
            formatNodesToElements(bpmnConfCommonElementVos, rebuildNodesList, nextNodeTo, numMap.get("nodeCode"), numMap.get("sequenceFlowNum"), numMap);
        }*/

	    return numMap;

    }

    /**
     * get next nodeTo
     *
     * @param nodeVo
     * @return
     */
    private String getNodeTo(BpmnNodeVo nodeVo) {
        return Optional.ofNullable(nodeVo.getParams()).map(BpmnNodeParamsVo::getNodeTo).orElse(null);
    }

    /**
     * get next node
     *
     * @param nodes
     * @param nodeTo
     * @return
     */
    private BpmnNodeVo getNextNodeVo(List<BpmnNodeVo> nodes, String nodeTo, List<BpmnNodeVo> ccNodeList) {
        List<BpmnNodeVo> nextNodeVo = nodes
                .stream()
                .filter(o -> o.getNodeId().equals(nodeTo))
                .collect(Collectors.toList());
		if (!CollectionUtils.isEmpty(ccNodeList)) {
			List<BpmnNodeVo> ccNextNodes = ccNodeList.stream().filter(o -> o.getNodeId().equals(nodeTo)).collect(Collectors.toList());
			if (ObjectUtils.isEmpty(nextNodeVo)) {
				if (!ObjectUtils.isEmpty(ccNextNodes)){
					return ccNextNodes.get(0);
				}
			}
		}
        return nextNodeVo.get(0);
    }
    private List<BpmnNodeVo> getNodeFroms(List<BpmnNodeVo> nodes,BpmnNodeVo currentNode){
        if(currentNode==null){
            throw new JiMuBizException("can not set null to current node");
        }
        List<BpmnNodeVo> results=new ArrayList<>();
        for (BpmnNodeVo node : nodes) {
            if(node.getParams().getNodeTo()!=null&&node.getParams().getNodeTo().equals(currentNode.getNodeId())){
                results.add(node);
            }
        }
        return results;
    }

}
