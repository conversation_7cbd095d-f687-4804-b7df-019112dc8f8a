package org.openoa.engine.vo.templategroup.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import org.openoa.base.dto.PageDto;
import org.openoa.base.util.PageUtils;

import java.io.Serializable;

/**
 * 模板分组
 */
@Data
public class UserMessagePageReq implements Serializable {

	private PageDto pageDto = PageUtils.getPageDto(new Page());

    /**
     * 是否已读 1-已读 0-未读 默认1
     */
    private Integer isRead;

}
