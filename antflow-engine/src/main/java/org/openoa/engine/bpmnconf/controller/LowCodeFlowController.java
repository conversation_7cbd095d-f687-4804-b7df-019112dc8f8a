package org.openoa.engine.bpmnconf.controller;


import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.NodePropertyEnum;
import org.openoa.base.dto.PageDto;
import org.openoa.base.entity.MemberControlVo;
import org.openoa.base.entity.Result;
import org.openoa.base.exception.JiMuBizException;
import org.openoa.base.vo.*;
import org.openoa.engine.bpmnconf.confentity.BpmnConfLfFormdata;
import org.openoa.engine.bpmnconf.confentity.TBpmnMemberControl;
import org.openoa.engine.bpmnconf.service.TBpmnMemberControlService;
import org.openoa.engine.bpmnconf.service.biz.LowCodeFlowBizService;
import org.openoa.engine.lowflow.service.BpmnConfLFFormDataBizServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/lowcode")
public class LowCodeFlowController {
    @Autowired
    private BpmnConfLFFormDataBizServiceImpl lfFormDataBizService;
    @Autowired(required = false)
    private LowCodeFlowBizService lowCodeFlowBizService;


    @Autowired
    private TBpmnMemberControlService tBpmnMemberControlService;

    /**
     * 获取全部 LF FormCodes 在流程设计时选择使用
     *
     * @return
     */
    @GetMapping("/getLowCodeFlowFormCodes")
    public Result<List<BaseKeyValueStruVo>> getLowCodeFormCodes() {
        return Result.newSuccessResult(lowCodeFlowBizService.getLowCodeFlowFormCodes());
    }

    /**
     * 获取LF FormCode Page List 模板列表使用
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/getLFFormCodePageList")
    public ResultAndPage<BaseKeyValueStruVo> getLFFormCodePageList(@RequestBody DetailRequestDto requestDto) {
        PageDto pageDto = requestDto.getPageDto();
        TaskMgmtVO taskMgmtVO = requestDto.getTaskMgmtVO();
        return lowCodeFlowBizService.selectLFFormCodePageList(pageDto, taskMgmtVO);
    }

	/**
	 * 获取申请记录分页列表使用
	 *
	 * @param applyRecordReqVo 请求和分页信息
	 * @return 模板分组分页列表
	 */
	@PostMapping("/getApplyRecordPageList")
	public ResultAndPage<ApplyRecordVo> getApplyRecordPageList(@RequestBody ApplyRecordReqVo applyRecordReqVo) {
		return lowCodeFlowBizService.selectApplyRecordPageList(applyRecordReqVo);
	}


	/**
	 * 获取首页模板分组展示三个模板模块
	 * @param homePageTemplateReqVo 请求和分页信息
	 * @return 模板分组分页列表
	 */
	@PostMapping("/homePage/getTemplateGroupListPage")
	public ResultAndPage<HomePageTemplateVo> getTemplateGroupListPage(@Valid @RequestBody HomePageTemplateReqVo homePageTemplateReqVo) {
		return lowCodeFlowBizService.getHomePageList(homePageTemplateReqVo);
	}

    /**
     * 获取 已设计流程并且启用的 LF FormCode Page List 发起页面使用
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/getLFActiveFormCodePageList")
    public ResultAndPage<BaseKeyValueStruVo> getLFActiveFormCodePageList(@RequestBody DetailRequestDto requestDto) {
        PageDto pageDto = requestDto.getPageDto();
        TaskMgmtVO taskMgmtVO = requestDto.getTaskMgmtVO();
        return lowCodeFlowBizService.selectLFActiveFormCodePageList(pageDto, taskMgmtVO);
    }

    /**
     * 低代码表单根据formcode查询对应的表单框架
     *
     * @param formCode
     * @return
     */
    @GetMapping("/getformDataByFormCode")
    public Result<String> getLFFormDataByFormCode(String formCode) {
        if (StringUtils.isEmpty(formCode)) {
            throw new JiMuBizException("请传入formcode");
        }
        BpmnConfLfFormdata lfFormDataByFormCode = lfFormDataBizService.getLFFormDataByFormCode(formCode);
        if (lfFormDataByFormCode == null) {
            throw new JiMuBizException("未查询到formcode对应的表单框架，请检查是否启动");
        }

        Result<String> result = Result.newSuccessResult(lfFormDataByFormCode.getFormdata());

        List<TBpmnMemberControl> tBpmnMemberControlList = tBpmnMemberControlService.getBpmnMemberControlInfo(formCode);
        if(!CollectionUtils.isEmpty(tBpmnMemberControlList)){
            List<MemberControlVo> controlVoList = tBpmnMemberControlList.stream()
                    .map(i -> new MemberControlVo(NodePropertyEnum.NODE_PROPERTY_MEMBER_CONTROL.getCode(), i.getFieldId(), i.getNodeId())).collect(Collectors.toList());
            result.setMemberControlVoList(controlVoList);
        }
        return result;
    }

    @PostMapping("/createLowCodeFormCode")
    public Result createLowCodeFormCode(@RequestBody BaseKeyValueStruVo vo) {
	    /*if (Objects.isNull(vo.getTemplateGroupId())) {
			return Result.newFailureResult("10001", "模板分组id不能为空");
	    }*/
        return Result.newSuccessResult(lowCodeFlowBizService.addFormCode(vo));
    }

}
