package org.openoa.engine.bpmnconf.service.biz;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.ProcessOperationEnum;
import org.openoa.base.constant.enums.ProcessStateEnum;
import org.openoa.base.constant.enums.YesOrNoEnum;
import org.openoa.base.entity.BpmBusinessProcess;
import org.openoa.base.entity.BpmProcessName;
import org.openoa.base.exception.JiMuBizException;
import org.openoa.base.interf.FormOperationAdaptor;
import org.openoa.base.interf.ProcessOperationAdaptor;
import org.openoa.base.util.DateUtil;
import org.openoa.base.util.SecurityUtils;
import org.openoa.base.vo.BaseIdTranStruVo;
import org.openoa.base.vo.BpmnStartConditionsVo;
import org.openoa.base.vo.BusinessDataVo;
import org.openoa.engine.bpmnconf.confentity.BpmnNode;
import org.openoa.engine.bpmnconf.confentity.BpmnNodePersonnelConf;
import org.openoa.engine.bpmnconf.confentity.BpmnNodePersonnelEmplConf;
import org.openoa.engine.bpmnconf.service.impl.BpmnNodePersonnelConfServiceImpl;
import org.openoa.engine.bpmnconf.service.impl.BpmnNodePersonnelEmplConfServiceImpl;
import org.openoa.engine.bpmnconf.service.impl.BpmnNodeServiceImpl;
import org.openoa.engine.factory.FormFactory;
import org.openoa.engine.lowflow.vo.UDLFApplyVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * process submit
 */
@Service
@Slf4j
public class SubmitProcessImpl implements ProcessOperationAdaptor {
    @Autowired
    private FormFactory formFactory;
    @Autowired
    private BpmnConfCommonServiceImpl bpmnConfCommonService;
    @Autowired
    protected BpmBusinessProcessServiceImpl bpmBusinessProcessService;

    @Autowired
    private BpmProcessNameServiceImpl bpmProcessNameService;
	@Autowired
	private BpmnNodeServiceImpl bpmnNodeService;
	@Autowired
	private BpmnNodePersonnelConfServiceImpl bpmnNodePersonnelConfService;
	@Autowired
	private BpmnNodePersonnelEmplConfServiceImpl bpmnNodePersonnelEmplConfService;

    @Override
    public void doProcessButton(BusinessDataVo businessDataVo) {
        log.info("Start submit process. param:{}", JSON.toJSONString(businessDataVo));
        FormOperationAdaptor formAdapter = formFactory.getFormAdaptor(businessDataVo);

        BusinessDataVo vo = businessDataVo;
        if(!Boolean.TRUE.equals(businessDataVo.getIsMigration())){
            vo=formAdapter.submitData(businessDataVo);
        }
        // call the process's launch method to get launch parameters
        BpmnStartConditionsVo bpmnStartConditionsVo = formAdapter.launchParameters(vo);
        bpmnStartConditionsVo.setBusinessDataVo(vo);
        bpmnStartConditionsVo.setApproversList(Optional.ofNullable(businessDataVo.getApproversList()).orElse(Maps.newHashMap()));
        String processNumber=businessDataVo.getFormCode() + "_" + vo.getBusinessId();
        if(Boolean.TRUE.equals(businessDataVo.getIsMigration())){
            processNumber=businessDataVo.getProcessNumber();
        }
        bpmnStartConditionsVo.setProcessNum(processNumber);
        bpmnStartConditionsVo.setEntryId(vo.getEntityName() + ":" + vo.getBusinessId());
        bpmnStartConditionsVo.setBusinessId(vo.getBusinessId());
        if(Boolean.TRUE.equals(businessDataVo.getIsMigration())){
            bpmnStartConditionsVo.setIsMigration(vo.getIsMigration());
        }else{
            String entryId = vo.getEntityName() + ":" + vo.getBusinessId();
            if (!bpmBusinessProcessService.checkProcessData(entryId)) {
                throw new JiMuBizException("the process has already been submitted！");
            }
        }

        //process's name
        String processName = Optional
                .ofNullable(bpmProcessNameService.getBpmProcessName(businessDataVo.getFormCode()))
                .orElse(new BpmProcessName()).getProcessName();
        //apply user info
        String applyName = SecurityUtils.getLogInEmpName();
        //save business and process information
        if(!Boolean.TRUE.equals(businessDataVo.getIsMigration())){

	        // 解析表单中的提交人
	        Map<String, Object> lfFields = ((UDLFApplyVo) vo).getLfFields();
	        String submitUserFieldId = vo.getSubmitUserFieldId();
	        if (StringUtils.isNotBlank(submitUserFieldId) && !CollectionUtils.isEmpty(lfFields)) {
		        String submitUser = "";
		        vo.setSubmitUserFieldId(submitUserFieldId);
		        Object lfFieldsVal = lfFields.get(submitUserFieldId);
		        if (Objects.nonNull(lfFieldsVal)) {
			        if (submitUserFieldId.startsWith("member")) {
				        JSONArray arr = JSONArray.parseArray(JSON.toJSONString(lfFieldsVal));
				        if (arr.size() > 1) {
					        throw new JiMuBizException("提交人只能选一个");
				        }
				        String submitUserNames = arr.stream().map(obj -> ((JSONObject) obj).getString("name")).filter(Objects::nonNull).collect(
					        Collectors.joining(","));
				        String submitUserIds = arr.stream().map(obj -> ((JSONObject) obj).getString("id")).filter(Objects::nonNull).collect(Collectors.joining(","));
				        log.info("doButton Aspect解析到提交人成员表单控件，提交人id={}, 提交人名称={}", submitUserIds, submitUserNames);
				        submitUser = submitUserNames;
				        vo.setSubmitUserId(submitUserIds);
			        } else if (submitUserFieldId.startsWith("input")) {
				        submitUser = lfFieldsVal.toString();
				        log.info("doButton Aspect解析到提交人文本表单控件，提交人名称={}", submitUser);
			        } else {
				        log.warn("解析表单中的提交人失败，没有对应的表单类型: {}", submitUserFieldId);
			        }
		        }
		        vo.setSubmitUserName(submitUser);
	        }

            bpmBusinessProcessService.addBusinessProcess(BpmBusinessProcess.builder()
                    .businessId(vo.getBusinessId())
                    .processinessKey(businessDataVo.getFormCode())
                    .businessNumber(processNumber)
                    .isLowCodeFlow(vo.getIsLowCodeFlow())
                    .createUser(businessDataVo.getStartUserId())
                    .userName(businessDataVo.getStartUserName())
                    .createTime(new Date())
                    .processDigest(vo.getProcessDigest())
                    .processState(ProcessStateEnum.HANDLING_STATE.getCode())
                    .entryId(vo.getEntityName() + ":" + vo.getBusinessId())
                    .description(applyName + "-" + processName)
                    .dataSourceId(vo.getDataSourceId())
                    .version(businessDataVo.getBpmnCode())
                    .approveCode(DateUtil.getCompressDateWithMinute(null))
		            .templateId(vo.getTemplateId())
		            .templateGroupId(vo.getTemplateGroupId())
		            .submitUserId(vo.getSubmitUserId())
		            .submitUserName(vo.getSubmitUserName())
                    .build());
            //the process number is predictable
            businessDataVo.setProcessNumber(businessDataVo.getFormCode() + "_" + vo.getBusinessId());
        }
		// 提前将自选抄送人存到t_bpmn_node_personnel_empl_conf
	    Map<String, List<BaseIdTranStruVo>> nodeIdApproversMap = businessDataVo.getApproversList();
		if (!CollectionUtils.isEmpty(nodeIdApproversMap)) {
			Map<String, String> nodeIdCcMap = Maps.newHashMap();
			for (Map.Entry<String, List<BaseIdTranStruVo>> entry : nodeIdApproversMap.entrySet()) {
				String nodeId = entry.getKey();
				List<BaseIdTranStruVo> empIds = entry.getValue();
				BpmnNode ccSelfSelectNode = bpmnNodeService.lambdaQuery()
					.eq(BpmnNode::getCcSelfSelectFlag, 1).eq(BpmnNode::getId, nodeId).one();
				if (Objects.isNull(ccSelfSelectNode)) {
					continue;
				}
				Integer personnelConfId = Optional.ofNullable(
					bpmnNodePersonnelConfService.lambdaQuery().select(BpmnNodePersonnelConf::getId).eq(BpmnNodePersonnelConf::getBpmnNodeId, ccSelfSelectNode.getId()).one())
					.map(BpmnNodePersonnelConf::getId).orElse(null);
				if (Objects.nonNull(personnelConfId) && !CollectionUtils.isEmpty(empIds)) {
					List<BpmnNodePersonnelEmplConf> pceEntityList = new ArrayList<>();
					empIds.forEach(empId -> {
						BpmnNodePersonnelEmplConf bpmnNodePersonnelEmplConf = new BpmnNodePersonnelEmplConf();
						bpmnNodePersonnelEmplConf.setBpmnNodePersonneId(personnelConfId);
						bpmnNodePersonnelEmplConf.setEmplId(empId.getId());
						bpmnNodePersonnelEmplConf.setEmplName(empId.getName());
						bpmnNodePersonnelEmplConf.setEhrSource(empId.getEhrSource());
						bpmnNodePersonnelEmplConf.setControlDataType(1);
						bpmnNodePersonnelEmplConf.setIsDel(YesOrNoEnum.NO.getCode());
						bpmnNodePersonnelEmplConf.setCreateUser(applyName);
						bpmnNodePersonnelEmplConf.setUpdateUser(applyName);
						bpmnNodePersonnelEmplConf.setNodeId(nodeId);
						pceEntityList.add(bpmnNodePersonnelEmplConf);
					});
					bpmnNodePersonnelEmplConfService.saveBatch(pceEntityList);
					nodeIdCcMap.put(nodeId, JSON.toJSONString(pceEntityList));
					businessDataVo.setCcSelftSelectFlag(1);
				}

			}
			businessDataVo.setNodeIdCcMap(nodeIdCcMap);
		}
	    bpmnConfCommonService.startProcess(businessDataVo, bpmnStartConditionsVo);
    }

    @Override
    public void setSupportBusinessObjects() {
        addSupportBusinessObjects(ProcessOperationEnum.BUTTON_TYPE_SUBMIT);
    }
}
