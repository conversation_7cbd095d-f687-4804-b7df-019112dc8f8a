package org.openoa.engine.vo.templategroup.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import org.openoa.base.dto.PageDto;
import org.openoa.base.util.PageUtils;

import java.io.Serializable;

/**
 * 模板分组
 */
@Data
public class BpmApproveTemplateGroupReq implements Serializable {

	private PageDto pageDto = PageUtils.getPageDto(new Page());

    /**
     * 模板分组名称
     */
    private String name;

}
