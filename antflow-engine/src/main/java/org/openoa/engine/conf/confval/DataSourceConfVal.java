//package org.openoa.engine.conf.confval;
//
//import lombok.Data;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Configuration;
//
//@Data
//@Configuration
//public class DataSourceConfVal {
//    @Value("${spring.datasource.url}")
//    private String url;
//
//    @Value("${spring.datasource.username}")
//    private String username;
//
//    @Value("${spring.datasource.password}")
//    private String password;
//
//    @Value("${spring.datasource.driver-class-name}")
//    private String driverClassName;
//
//    @Value("${spring.datasource.druid.initial-size}")
//    private int initialSize;
//
//    @Value("${spring.datasource.druid.min-idle}")
//    private int minIdle;
//
//    @Value("${spring.datasource.druid.max-active}")
//    private int maxActive;
//
//    @Value("${spring.datasource.druid.max-wait}")
//    private int maxWait;
//
//    /**
//     * 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
//     */
//    @Value("60000")
//    private int timeBetweenEvictionRunsMillis;
//    /**
//     * 配置一个连接在池中最小生存的时间，单位是毫秒
//     */
//    @Value("300000")
//    private int minEvictableIdleTimeMillis;
//
//
//    @Value("${spring.datasource.druid.removeAbandoned}")
//    private Boolean removeAbandoned;
//    @Value("${spring.datasource.druid.removeAbandonedTimeout}")
//    private Integer removeAbandonedTimeout;
//}
