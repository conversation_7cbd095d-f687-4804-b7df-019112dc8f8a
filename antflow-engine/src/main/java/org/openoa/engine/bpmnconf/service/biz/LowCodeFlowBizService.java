package org.openoa.engine.bpmnconf.service.biz;

import org.openoa.base.dto.PageDto;
import org.openoa.base.entity.Result;
import org.openoa.base.vo.*;

import java.util.List;

public interface LowCodeFlowBizService {
    //todo cbcbu
    List<BaseKeyValueStruVo> getLowCodeFlowFormCodes();
    //获取LF FormCode Page List 模板列表使用
    ResultAndPage<BaseKeyValueStruVo> selectLFFormCodePageList(PageDto pageDto, TaskMgmtVO taskMgmtVO);

    ResultAndPage<BaseKeyValueStruVo> selectLFActiveFormCodePageList(PageDto pageDto, TaskMgmtVO taskMgmtVO);

    Integer addFormCode(BaseKeyValueStruVo vo);

	/**
	 * 获取申请记录分页列表
	 */
	ResultAndPage<ApplyRecordVo> selectApplyRecordPageList(ApplyRecordReqVo applyRecordReqVo);

	/**
	 * 获取首页模板分组展示三个模板模块
	 */
	ResultAndPage<HomePageTemplateVo> getHomePageList(HomePageTemplateReqVo applyRecordReqVo);

}
