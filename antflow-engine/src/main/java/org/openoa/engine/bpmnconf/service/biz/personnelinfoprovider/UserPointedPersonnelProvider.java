package org.openoa.engine.bpmnconf.service.biz.personnelinfoprovider;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.openoa.base.constant.enums.CustomFieldEnum;
import org.openoa.base.entity.User;
import org.openoa.base.entity.UserRole;
import org.openoa.base.exception.JiMuBizException;
import org.openoa.base.interf.BpmBusinessProcessService;
import org.openoa.base.interf.BpmnPersonnelProviderService;
import org.openoa.base.mapper.UserMapper;
import org.openoa.base.service.TUserRoleService;
import org.openoa.base.vo.*;
import org.openoa.common.util.AssigneeVoBuildUtils;
import org.openoa.engine.bpmnconf.confentity.BpmnNodePersonnelEmplConf;
import org.openoa.engine.bpmnconf.service.impl.BpmnNodePersonnelEmplConfServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 * @Description //TODO $
 * @Date 2022-05-01 10:27
 * @Param
 * @return
 * @Version 1.0
 */
@Component
@Slf4j
public class UserPointedPersonnelProvider implements BpmnPersonnelProviderService {
    @Autowired
    private AssigneeVoBuildUtils assigneeVoBuildUtils;

    @Resource
    private TUserRoleService tUserRoleService;
	@Resource
	private UserMapper userMapper;
	@Resource
	private BpmBusinessProcessService bpmBusinessProcessService;
	@Resource
	private BpmnNodePersonnelEmplConfServiceImpl bpmnNodePersonnelEmplConfService;
    @Override
    public List<BpmnNodeParamsAssigneeVo> getAssigneeList( BpmnNodeVo bpmnNodeVo, BpmnStartConditionsVo startConditionsVo) {
        if(bpmnNodeVo==null){
            throw new JiMuBizException("node can not be null!");
        }
        BpmnNodePropertysVo propertysVo = bpmnNodeVo.getProperty();
        if (ObjectUtils.isEmpty(propertysVo)) {
            throw new JiMuBizException("appointed assignee doest not meet basic condition,can not go on");
        }
        String elementName=bpmnNodeVo.getNodeName();
        if(Strings.isNullOrEmpty(elementName)){
            elementName="指定人员";
        }
        if(bpmnNodeVo.getIsOutSideProcess()!=null&&bpmnNodeVo.getIsOutSideProcess().equals(1)){
            List<BaseIdTranStruVo> emplList = bpmnNodeVo.getProperty().getEmplList();
            if(CollectionUtils.isEmpty(emplList)){
                throw new JiMuBizException("thirdy party process role node has no employee info");
            }
          return assigneeVoBuildUtils.buildVOs(emplList,elementName,false);
        }
        List<String> emplIds = propertysVo.getEmplList().stream()
                .filter(e -> Objects.isNull(e.getControlDataType()))
                .map(BaseIdTranStruVo::getId).collect(Collectors.toList());
        if(CustomFieldEnum.TAG.getNo().equals(propertysVo.getEmplList().get(0).getControlDataType())){
            List<UserRole> userRoles = tUserRoleService.list(new LambdaQueryWrapper<UserRole>().in(UserRole::getRoleId, emplIds));
            emplIds = userRoles.stream().map(UserRole::getUserId).collect(Collectors.toList());
        }
		log.info("--------------->assigneeVoBuildUtils.buildVos emplIds:{}, 模板id={}", emplIds, bpmnNodeVo.getConfId());
	    List<BpmnNodeParamsAssigneeVo> bpmnNodeParamsAssigneeVos = new ArrayList<>();
		if (CollectionUtils.isEmpty(emplIds)) {
			log.info("---------------> emplIds is empty");
			return bpmnNodeParamsAssigneeVos;
		} else {
			List<String> fixedEmplIds = new ArrayList<>();
			for (String emplId : emplIds) {
				User user = userMapper.getUserById(emplId);
				if (user != null) {
					fixedEmplIds.add(emplId);
					continue;
				}
				String beforeEmpId = emplId;
				String newEmpId;
				if (emplId.startsWith("jt")) {
					newEmpId = emplId.substring(2);
				} else {
					newEmpId = "jt" + emplId;
				}

				User userFixed = userMapper.getUserById(newEmpId);
				if (userFixed != null) {
					log.info("===> 发起流程，用户找不到，自动修复模板和流程中的历史数据，beforeEmpId: {}, afterEmpId: {}", beforeEmpId, newEmpId);

					// 修复流程中历史数据
					bpmBusinessProcessService.updateMergedUserInfoHistory(Lists.newArrayList(beforeEmpId), newEmpId);

					// 修改节点中指定的审核人
					bpmnNodePersonnelEmplConfService.lambdaUpdate()
						.eq(BpmnNodePersonnelEmplConf::getEmplId, beforeEmpId)
						.set(BpmnNodePersonnelEmplConf::getEmplId, newEmpId)
						.set(BpmnNodePersonnelEmplConf::getEhrSource, userFixed.getEhrSource())
						.update();

					fixedEmplIds.add(newEmpId);
				} else {
					log.error("===> 看到可忽略，发起流程，用户找不到，可尝试手动调用truncateEmpInfo接口修复数据，userFixed is null, emplId: {}", emplId);
				}
			}
			bpmnNodeParamsAssigneeVos = assigneeVoBuildUtils.buildVos(fixedEmplIds, elementName,false);
		}
        return bpmnNodeParamsAssigneeVos;
    }
}
