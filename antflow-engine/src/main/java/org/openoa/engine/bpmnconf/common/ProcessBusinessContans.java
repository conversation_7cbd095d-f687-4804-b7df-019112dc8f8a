package org.openoa.engine.bpmnconf.common;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.*;
import org.openoa.base.dto.wecom.NodeHeadActionDto;
import org.openoa.base.entity.BpmBusinessProcess;
import org.openoa.base.entity.Employee;
import org.openoa.base.exception.JiMuBizException;
import org.openoa.base.util.SecurityUtils;
import org.openoa.base.vo.BpmVerifyInfoVo;
import org.openoa.base.vo.LFFieldControlVO;
import org.openoa.base.vo.ProcessRecordInfoVo;
import org.openoa.common.entity.BpmVariableMultiplayer;
import org.openoa.common.entity.BpmVariableSingle;
import org.openoa.common.service.BpmVariableMultiplayerServiceImpl;
import org.openoa.common.service.BpmVariableSingleServiceImpl;
import org.openoa.engine.bpmnconf.confentity.BpmProcessForward;
import org.openoa.engine.bpmnconf.confentity.BpmVariable;
import org.openoa.engine.bpmnconf.confentity.BpmVariableSignUpPersonnel;
import org.openoa.engine.bpmnconf.confentity.BpmnNode;
import org.openoa.engine.bpmnconf.mapper.BpmBusinessProcessMapper;
import org.openoa.engine.bpmnconf.service.impl.*;
import org.openoa.engine.vo.ProcessInforVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 0.5
 */
@Slf4j
@Component
public class ProcessBusinessContans extends ProcessServiceFactory {
    @Autowired
    private BpmProcessForwardServiceImpl processForwardService;


    @Autowired
    private HistoryService historyService;
    @Autowired
    private EmployeeServiceImpl employeeService;
    @Autowired
    private BpmnNodeLfFormdataFieldControlServiceImpl bpmnNodeLfFormdataFieldControlService;
	@Autowired
	private BpmnNodeServiceImpl bpmnNodeService;
	@Autowired
	private BpmVariableMultiplayerServiceImpl bpmnVariableMultiplayerService;
	@Autowired
	private BpmVariableServiceImpl bpmnVariableService;
	@Autowired
	private BpmVariableSignUpPersonnelServiceImpl bpmVariableSignUpPersonnelService;
	@Autowired
	private BpmVerifyInfoServiceImpl bpmVerifyInfoService;
	@Autowired
	private BpmBusinessProcessMapper businessProcessMapper;
	@Autowired
	private BpmVariableSingleServiceImpl bpmVariableSingleService;





	/**
     * query process record info
     */
    public ProcessRecordInfoVo processInfo(BpmBusinessProcess bpmBusinessProcess) {
        ProcessRecordInfoVo processInfoVo = new ProcessRecordInfoVo();
        if (bpmBusinessProcess == null) {
            return processInfoVo;
        }
        //check permissions
        if (!this.showProcessData(bpmBusinessProcess.getBusinessNumber())) {
            throw new JiMuBizException("00", "current user has no access right！");
        }
        //set task state
        processInfoVo.setTaskState(ProcessStateEnum.getDescByCode(bpmBusinessProcess.getProcessState()));
        //process's verify info
        processInfoVo.setVerifyInfoList(verifyInfoService.verifyInfoList(bpmBusinessProcess));
        //set process desc
        processInfoVo.setProcessTitle(bpmBusinessProcess.getDescription());

        Employee employee = employeeService.qryLiteEmployeeInfoById(bpmBusinessProcess.getCreateUser());
        processInfoVo.setEmployee(employee);
        processInfoVo.setCreateTime(bpmBusinessProcess.getCreateTime());
        //set start userId
        processInfoVo.setStartUserId(bpmBusinessProcess.getCreateUser());
        //set process Number
        processInfoVo.setProcessNumber(bpmBusinessProcess.getBusinessNumber());
        String processInstanceId = bpmBusinessProcess.getBusinessId();
        //modify forward data
        processForwardService.updateProcessForward(BpmProcessForward.builder()
                .processInstanceId(processInstanceId)
                .forwardUserId(SecurityUtils.getLogInEmpIdStr())
                .build());
        //modify notice
        userMessageService.readNode(processInstanceId);
        List<Task> list = taskService.createTaskQuery().processInstanceId(bpmBusinessProcess.getProcInstId()).taskAssignee(SecurityUtils.getLogInEmpId()).list();
        String taskDefKey = "";
        if (!ObjectUtils.isEmpty(list)) {
            taskDefKey = list.get(0).getTaskDefinitionKey();
            processInfoVo.setTaskId(list.get(0).getId());
            processInfoVo.setNodeId(taskDefKey);

        } else {
            if (Objects.equals(bpmBusinessProcess.getIsLowCodeFlow(), 1)) {
                List<HistoricTaskInstance> historicTaskInstances = historyService
                        .createHistoricTaskInstanceQuery()
                        .processInstanceId(bpmBusinessProcess
                                .getProcInstId()).
                        taskAssignee(SecurityUtils.getLogInEmpId())
                        .orderByHistoricTaskInstanceEndTime()
                        .desc()
                        .list();
                if (!CollectionUtils.isEmpty(historicTaskInstances)) {
                    taskDefKey = historicTaskInstances.get(0).getTaskDefinitionKey();
                }
            }
        }
	    Long variableId = Optional.ofNullable(bpmnVariableService.lambdaQuery().eq(BpmVariable::getProcessNum, processInfoVo.getProcessNumber()).last(" limit 1").one()).map(BpmVariable::getId).orElse(null);
	    String elementId = processInfoVo.getNodeId();
		// 加批人
	    BpmVariableSignUpPersonnel jiapiInfo = bpmVariableSignUpPersonnelService.lambdaQuery().select(BpmVariableSignUpPersonnel::getNodeId, BpmVariableSignUpPersonnel::getAssignee)
		     .eq(BpmVariableSignUpPersonnel::getVariableId, variableId).eq(BpmVariableSignUpPersonnel::getElementId, elementId).last(" limit 1").one();
		String jiapiUserId = Optional.ofNullable(jiapiInfo).map(BpmVariableSignUpPersonnel::getAssignee).orElse(null);
	    boolean jpUser = Objects.equals(jiapiUserId, SecurityUtils.getLogInEmpIdSafe());
	    if (Objects.equals(processInfoVo.getStartUserId(), SecurityUtils.getLogInEmpIdSafe()) && Objects.equals(elementId, ProcessEnum.START_TASK_KEY.getDesc())) {
		    Long confId = bpmBusinessProcess.getTemplateId();
		    Long startNodeId = Optional.ofNullable(bpmnNodeService.lambdaQuery()
			    .select(BpmnNode::getId)
			    .eq(BpmnNode::getConfId, confId)
			    .eq(BpmnNode::getNodeType, NodeTypeEnum.NODE_TYPE_START.getCode())
			    .last("limit 1").one()).map(BpmnNode::getId).orElse(null);
		    List<LFFieldControlVO> startUserControls = bpmnNodeLfFormdataFieldControlService
			    .getBaseMapper()
			    .getFieldControlByNodeId(startNodeId);
		    processInfoVo.setLfFieldControlVOs(startUserControls);
	    } else if (!jpUser && !StringUtils.isEmpty(taskDefKey) && Objects.equals(bpmBusinessProcess.getIsLowCodeFlow(), 1) && StringUtils.isNotBlank(processInfoVo.getNodeId())) {
	        String nodeId = Optional.ofNullable(bpmnVariableMultiplayerService.lambdaQuery()
		        .eq(BpmVariableMultiplayer::getElementId, elementId)
		        .eq(BpmVariableMultiplayer::getVariableId, variableId)
		        .last(" limit 1").one()).map(BpmVariableMultiplayer::getNodeId).orElse(null);
			if (StringUtils.isBlank(nodeId)) { // t_bpm_variable_multiplayer查不到再去t_bpm_variable_single查nodeId
				nodeId = Optional.ofNullable(bpmVariableSingleService.lambdaQuery()
					.eq(BpmVariableSingle::getElementId, elementId)
					.eq(BpmVariableSingle::getVariableId, variableId)
					.last(" limit 1").one()).map(BpmVariableSingle::getNodeId).orElse(null);
			}
			if (StringUtils.isNotBlank(nodeId)) {
				List<LFFieldControlVO> currentFieldControls = bpmnNodeLfFormdataFieldControlService
					.getBaseMapper()
					.getFieldControlByNodeId(Long.valueOf(nodeId));
				processInfoVo.setLfFieldControlVOs(currentFieldControls);
			}
        }
	    if (jpUser) { // 当前审批人是加批人，加批人的权限与当前审核人共享
		    Long jpNodeId = Optional.ofNullable(jiapiInfo).map(BpmVariableSignUpPersonnel::getNodeId).orElse(null);
			log.info("加批节点nodeId={}, 审批单号={}", jpNodeId, bpmBusinessProcess.getBusinessNumber());
		    if (Objects.nonNull(jpNodeId)) {
			    if (jpNodeId.equals(36199L)) {
				    jpNodeId = 36200L;
			    }
			    if (jpNodeId.equals(33458L)) {
				    jpNodeId = 33459L;
			    }
			    List<LFFieldControlVO> currentFieldControls = bpmnNodeLfFormdataFieldControlService.getBaseMapper().getFieldControlByNodeId(jpNodeId);
			    processInfoVo.setLfFieldControlVOs(currentFieldControls);
		    }
	    }
		if (StringUtils.isBlank(elementId)) {
			Task currentTask = Optional.ofNullable(taskService.createTaskQuery().processInstanceId(bpmBusinessProcess.getProcInstId()).list())
				.orElse(new ArrayList<>()).stream().findFirst().orElse(null);
			if (currentTask != null) { // 流程未结束，并且流程流转到的节点不是当前的审批人，此时返回的权限不能是当前节点审批人的表单权限，而是需要获取当前登录人对应的权限设置信息
				elementId = currentTask.getTaskDefinitionKey();
				if (!Objects.equals(currentTask.getAssignee(), SecurityUtils.getLogInEmpId())) { // 当前登录人不是当前节点的审核人，但是当前登录人是属于当前流程的审核人列表中 并且设置过权限
					BpmVerifyInfoVo verifyInfoVo = getBpmVerifyInfoVo(bpmBusinessProcess);
					if (verifyInfoVo != null) {
						// NodeHeadActionDto headAction = businessProcessMapper.getHeadAction(bpmBusinessProcess.getBusinessNumber(), verifyInfoVo.getTaskDefKey());
						NodeHeadActionDto headAction = businessProcessMapper.getHeadAction(bpmBusinessProcess.getBusinessNumber(), elementId);
						if (Objects.nonNull(headAction) && Objects.nonNull(headAction.getNodeId())) {
							List<LFFieldControlVO> currentFieldControls = bpmnNodeLfFormdataFieldControlService
								.getBaseMapper()
								.getFieldControlByNodeId(Long.valueOf(headAction.getNodeId()));
							log.info("当前登录人不是当前节点的审核人，但是当前登录人是属于当前流程的审核人列表中 并且设置过权限。流程编号={},elementId={},headAction={}", bpmBusinessProcess.getBusinessNumber(), elementId, JSON.toJSONString(headAction));
							processInfoVo.setLfFieldControlVOs(currentFieldControls);
						}
					}
				}
			} else { // 此时流程已结束(比如审批人不同意导致流程结束), 也要获取当前登录人对应的权限设置信息。 流程已结束和未结束的区别在于，流程已结束的，t_bpm_variable_multiplayer.element_id无法直接获取（获取方式看getNodeIdsByProcessNumber方法）
				BpmVerifyInfoVo verifyInfoVo = getBpmVerifyInfoVo(bpmBusinessProcess);
				if (verifyInfoVo != null) {
					Long loginUserNodeId = businessProcessMapper.getNodeIdsByProcessNumber(bpmBusinessProcess.getBusinessNumber(), verifyInfoVo.getRunInfoId(), verifyInfoVo.getVerifyUserId(), verifyInfoVo.getVerifyUserName());
					if (Objects.nonNull(loginUserNodeId)) {
						processInfoVo.setLfFieldControlVOs(bpmnNodeLfFormdataFieldControlService.getBaseMapper().getFieldControlByNodeId(loginUserNodeId));
					}
				}
			}
		}
        return processInfoVo;
    }

	private BpmVerifyInfoVo getBpmVerifyInfoVo(BpmBusinessProcess bpmBusinessProcess) {
		List<BpmVerifyInfoVo> searchBpmVerifyInfoVos = bpmVerifyInfoService.verifyInfoList(bpmBusinessProcess.getBusinessNumber(), bpmBusinessProcess.getProcInstId());
		BpmVerifyInfoVo verifyInfoVo = null;
		if (!CollectionUtils.isEmpty(searchBpmVerifyInfoVos)) {
			for (BpmVerifyInfoVo searchBpmVerifyInfoVo : searchBpmVerifyInfoVos) {
				if (Objects.equals(searchBpmVerifyInfoVo.getVerifyUserId(), SecurityUtils.getLogInEmpId())) {
					verifyInfoVo = searchBpmVerifyInfoVo;
					break;
				}
			}
		}
		return verifyInfoVo;
	}


    /**
     * delete process
     */
    public void deleteProcessInstance(String processInstanceId) {
        runtimeService.deleteProcessInstance(processInstanceId, "process ending");
    }



    /**
     * verify current logged in user's data right
     *
     * @param processCode that is process number
     * @return
     */
    public boolean showProcessData(String processCode) {

        BpmBusinessProcess bpmBusinessProcess = bpmBusinessProcessService.getBpmBusinessProcess(processCode);
        //monitor,view,process's admin,super admin,historical approvers and forward user
        if (!ObjectUtils.isEmpty(bpmBusinessProcess)) {
            List<HistoricTaskInstance> taskInstanceList = historyService.createHistoricTaskInstanceQuery().processInstanceId(bpmBusinessProcess.getProcInstId()).list();
            List<String> list = taskInstanceList.stream().filter(s -> !ObjectUtils.isEmpty(s)).map(HistoricTaskInstance::getAssignee).collect(Collectors.toList());
            if (list.contains(SecurityUtils.getLogInEmpIdStr())) {
                return true;
            }
            //todo redesign
            return true;
        }
        return true;
    }

    /**
     * app route url build
     *
     * @param processKey process key
     * @param fromCode   form code
     * @return
     * @throws UnsupportedEncodingException
     */
    public String applyRoute(String processKey, String fromCode, boolean isOutSide) {
        String roure = "";
        try {
            String inParameter = URLEncoder.encode("{\"processKey\":\"" + processKey + "\",\"formCode\":\"" + fromCode + "\"}", "UTF-8");
            roure = "oaapp://oa.app/page?param={\"type\":\"H5\",\"pageName\":\"{申请类型}\"," +
                    "\"pageURL\":\"/{申请类型}/{路由}/{工作流入参}\"}";
            if (isOutSide) {
                fromCode = "OUTSIDE_WMA";
            }
            String routePath = "apply";


            roure = StringUtils.replaceEach(roure,
                    new String[]{"{申请类型}", "{路由}", "{工作流入参}"},
                    new String[]{fromCode, routePath, inParameter});
            roure = URLEncoder.encode(roure, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return roure;
    }
    /***
     * pc route
     */
    public String pcApplyRoute(String processKey, String formCode, String processNumber, Integer type, boolean isOutside) {

        //if it is an outside process,then return outside url
        if (isOutside) {//todo third party url,to be redesigned
            return StringUtils.join("/user/workflow/detail/third-party/", formCode, "/", processNumber);
        }

        String pcUrl;
        if (type.equals(ProcessJurisdictionEnum.VIEW_TYPE.getCode())) {
            pcUrl = "/user/workflow/detail/" + formCode + "/" + processKey + "/" + processNumber;
        } else if (type.equals(ProcessJurisdictionEnum.CREATE_TYPE.getCode())) {
            pcUrl = "/user/workflow/Upcoming/check/" + formCode + "/" + processKey + "/" + processNumber;
        } else {
           //todo
            pcUrl = "/user/workflow/Upcoming/apply/" + formCode + "/" + processKey + "/" + processNumber;
        }
        return pcUrl;
    }
    /**
     * get route
     *
     * @param type    ProcessNoticeEnum
     * @param inforVo
     * @return
     */
    public String getRoute(Integer type, ProcessInforVo inforVo, boolean isOutside) {
        String url = "";
        //email
        if (type.equals(ProcessNoticeEnum.EMAIL_TYPE.getCode())) {
            url = this.pcApplyRoute(inforVo.getProcessinessKey(), inforVo.getFormCode(), inforVo.getBusinessNumber(), inforVo.getType(), isOutside);
        } else {
            url = this.detailRoute(inforVo.getFormCode(), inforVo.getBusinessNumber(), inforVo.getType(), isOutside);
        }
        return url;
    }

    /**
     * app approve/view route url
     *
     * @param formCode      form code
     * @param processNumber process number
     * @param type          type(1：view，2：operate)
     * @return
     * @throws UnsupportedEncodingException
     */
    public String detailRoute(String formCode, String processNumber, Integer type, boolean isOutSide) {
        String detail = "";
        String appUrl = "";
        if (type.equals(ProcessJurisdictionEnum.CONTROL_TYPE.getCode())) {
            type = ProcessJurisdictionEnum.CREATE_TYPE.getCode();
            appUrl = "apply";

        } else {
            appUrl = "approval";

            if (!StringUtils.isEmpty(processNumber)) {
                BpmBusinessProcess bpmBusinessProcess = bpmBusinessProcessService.getBpmBusinessProcess(processNumber);
                appUrl = appUrl.concat("_");
                if(bpmBusinessProcess.getVersion()!=null){
                    appUrl=appUrl.concat(bpmBusinessProcess.getVersion().toString());
                }
            }
        }

        try {
            String inParameter = URLEncoder.encode("{\"formCode\":\"" + formCode + "\",\"processNumber\":\"" + processNumber + "\",\"type\":" + type + "}", "UTF-8");
            //todo to be resesigned
            detail = "";
            if (isOutSide) {
                formCode = "OUTSIDE_WMA";
            }
            detail = StringUtils.replaceEach(detail,
                    new String[]{"{申请类型}", "{路由}", "{工作流入参}"},
                    new String[]{formCode, appUrl, inParameter});
            detail = URLEncoder.encode(detail, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return detail;
    }
    /**
     * check current app version true for current version false for old version,not implemented yet at the moment
     * @return
     */
    public boolean checkAppVersionByCurrentUser() {
        //todo to be implemented
        return false;
    }
}
