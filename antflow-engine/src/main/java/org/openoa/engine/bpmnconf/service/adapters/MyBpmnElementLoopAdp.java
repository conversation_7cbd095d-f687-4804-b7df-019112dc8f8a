package org.openoa.engine.bpmnconf.service.adapters;


import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.NodePropertyEnum;
import org.openoa.base.constant.enums.ProcessNodeEnum;
import org.openoa.base.vo.*;
import org.openoa.common.util.BpmnElementUtils;
import org.openoa.engine.bpmnconf.service.MyBpmnElementAdaptor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MyBpmnElementLoopAdp extends MyBpmnElementAdaptor {


    @Override
    protected BpmnConfCommonElementVo getElementVo(BpmnNodePropertysVo property, BpmnNodeParamsVo params, Integer elementCode, String elementId) {
        return null;
    }

    @Override
    public void doFormatNodesToElements(List<BpmnConfCommonElementVo> bpmnConfCommonElementVos, BpmnNodeVo nodeVo, Integer nodeCode, Integer sequenceFlowNum, HashMap<String, Integer> numMap) {

        BpmnNodeParamsVo params = nodeVo.getParams();


        //filter assignees that have already been deduplicated
        List<BpmnNodeParamsAssigneeVo> paramsAssigneeVos = params.getAssigneeList()
                .stream()
                .filter(o -> o.getIsDeduplication() == 0)
                .collect(Collectors.toList());


        //compatibility with multiple approvals
        if (!CollectionUtils.isEmpty(paramsAssigneeVos)
                && paramsAssigneeVos.size() > 1
                && new Integer(1).equals(nodeVo.getProperty().getIsMultiPeople())) {
            Integer elementCode = nodeCode + 1;
            String elementId = ProcessNodeEnum.getDescByCode(elementCode);
            Integer elementSequenceFlowNum = sequenceFlowNum + 1;
           /* List<String> assignees = paramsAssigneeVos
                    .stream()
                    .map(BpmnNodeParamsAssigneeVo::getAssignee)
                    .collect(Collectors.toList());*/
            Map<String, String> assigneeMap = paramsAssigneeVos.stream()
                    .collect(Collectors.
                            toMap(BpmnNodeParamsAssigneeVo::getAssignee, BpmnNodeParamsAssigneeVo::getAssigneeName, (k1, k2) -> k1));


            //set element
            BpmnConfCommonElementVo elementVo = BpmnElementUtils.getMultiplayerSignElement(elementId,
                    paramsAssigneeVos.get(0).getElementName(),
                    StringUtils.join("loopUserSign", elementCode), new ArrayList<>(assigneeMap.keySet()),assigneeMap);

	        if (Objects.isNull(nodeVo.getButtons()) || Objects.isNull(nodeVo.getButtons().getStartPage()) || Objects.isNull(nodeVo.getButtons().getApprovalPage())) {
		        log.info("formatNodesToElements call2, nodeVo={}, elementVo={}", JSON.toJSONString(nodeVo), JSON.toJSONString(elementVo));
	        }
            //set element buttons
            setElementButtons(nodeVo, elementVo);

            elementVo.setNodeId(String.valueOf(nodeVo.getId()));
            elementVo.setLabelList(nodeVo.getLabelList());

            //set element message template
            elementVo.setTemplateVos(nodeVo.getTemplateVos());


            //set element approval remind
            elementVo.setApproveRemindVo(nodeVo.getApproveRemindVo());

            bpmnConfCommonElementVos.add(elementVo);

            //add flow element
            bpmnConfCommonElementVos.add(BpmnElementUtils.getSequenceFlow(elementSequenceFlowNum,
                    ProcessNodeEnum.getDescByCode(nodeCode), elementVo.getElementId()));

            nodeCode++;
            sequenceFlowNum++;

            numMap.put("nodeCode", nodeCode);
            numMap.put("sequenceFlowNum", sequenceFlowNum);

            return;
        }

        for (BpmnNodeParamsAssigneeVo bpmnNodeParamsAssigneeVo : paramsAssigneeVos) {
            Integer elementCode = nodeCode + 1;
            String elementId = ProcessNodeEnum.getDescByCode(elementCode);
            Integer elementSequenceFlowNum = sequenceFlowNum + 1;
            String assignee = bpmnNodeParamsAssigneeVo.getAssignee();
            String assigneeName = bpmnNodeParamsAssigneeVo.getAssigneeName();
            Map<String,String> singleAssigneeMap=new HashMap<>();
            singleAssigneeMap.put(assignee,assigneeName);
            BpmnConfCommonElementVo elementVo = BpmnElementUtils.getSingleElement(elementId, bpmnNodeParamsAssigneeVo.getElementName(),
                    StringUtils.join("loopUser", elementCode), assignee,singleAssigneeMap);

	        if (Objects.isNull(nodeVo.getButtons()) || Objects.isNull(nodeVo.getButtons().getStartPage()) || Objects.isNull(nodeVo.getButtons().getApprovalPage())) {
		        log.info("formatNodesToElements call3, nodeVo={}, elementVo={}", JSON.toJSONString(nodeVo), JSON.toJSONString(elementVo));
	        }
            //wet element buttons
            super.setElementButtons(nodeVo, elementVo);


            //set node message template
            elementVo.setTemplateVos(nodeVo.getTemplateVos());


            //set in node approval remind
            elementVo.setApproveRemindVo(nodeVo.getApproveRemindVo());

            bpmnConfCommonElementVos.add(elementVo);

            bpmnConfCommonElementVos.add(BpmnElementUtils.getSequenceFlow(elementSequenceFlowNum,
                    ProcessNodeEnum.getDescByCode(nodeCode), elementVo.getElementId()));

            nodeCode++;
            sequenceFlowNum++;

            numMap.put("nodeCode", nodeCode);
            numMap.put("sequenceFlowNum", sequenceFlowNum);
        }

    }

    @Override
    public void setSupportBusinessObjects() {
        addSupportBusinessObjects(NodePropertyEnum.NODE_PROPERTY_LOOP);
    }
}
