package org.openoa.engine.bpmnconf.confentity.templategroup;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data  
@TableName("t_bpmn_template_group_conf")
public class TemplateGroupConf implements Serializable {
  
    private static final long serialVersionUID = 1L;  
  
    /**  
     * 主键ID  
     */  
    @TableId(value = "id", type = IdType.AUTO)  
    private Long id;

	/**
	 * 分组名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 分组描述
	 */
	@TableField("description")
	private String description;

	/**
	 * 创建人
	 */
	@TableField("create_user")
	private String createUser;

	/**
	 * 是否删除 0-否 1-是
	 */
	@TableField("deleted")
	private Integer deleted;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private LocalDateTime updateTime;

}