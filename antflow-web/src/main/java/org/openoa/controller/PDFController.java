package org.openoa.controller;

import com.alibaba.fastjson2.JSON;
import com.itextpdf.text.Document;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.entity.Result;
import org.openoa.base.exception.JiMuBizException;
import org.openoa.base.vo.BusinessDataVo;
import org.openoa.service.PdfService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * @Description 描述
 * <AUTHOR>
 * @Date 2025-04-24 09:15
 */
@RequestMapping("/template/pdf")
@RestController
@Slf4j
public class PDFController {
	@Resource
	private PdfService pdfService;

	@RequestMapping(value = "/export", method = {RequestMethod.POST, RequestMethod.GET})
	public Result<Void> export(@RequestBody String values, HttpServletResponse response, OutputStream outputStream) {
		BusinessDataVo vo;
		if (StringUtils.isBlank(values)) {
			throw new JiMuBizException("PDF文件导出失败，缺少必要参数");
		} else {
			vo = JSON.parseObject(values, BusinessDataVo.class);
			if (vo == null) {
				throw new JiMuBizException("PDF文件导出失败，缺少必要参数");
			}
			if (StringUtils.isBlank(vo.getFormCode())) {
				throw new JiMuBizException("PDF文件导出失败，缺少必要参数:formCode");
			}
			if (StringUtils.isBlank(vo.getProcessNumber())) {
				throw new JiMuBizException("PDF文件导出失败，缺少必要参数:processNumber");
			}
			if (Objects.isNull(vo.getType())) {
				throw new JiMuBizException("PDF文件导出失败，缺少必要参数:type");
			}
			if (Objects.isNull(vo.getIsOutSideAccessProc())) {
				throw new JiMuBizException("PDF文件导出失败，缺少必要参数:isOutSideAccessProc");
			}
			if (Objects.isNull(vo.getIsLowCodeFlow())) {
				throw new JiMuBizException("PDF文件导出失败，缺少必要参数:isLowCodeFlow");
			}

		}
		String fileName = dateNowStr() + RandomStringUtils.randomNumeric(5) + ".pdf";
		try { // 设置响应头，控制浏览器下载该文件
			response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
		} catch (UnsupportedEncodingException e) {
			log.error("PDF文件导出失败，编码异常: {}", e.getMessage());
			throw new JiMuBizException("PDF文件导出失败，编码异常");
		}
		Document document = new Document(PageSize.A4);
		try {
			// 创建输出流
			OutputStream out = response.getOutputStream();
			PdfWriter writer = PdfWriter.getInstance(document, out);
			pdfService.export(document, writer, values);
		} catch (Exception e) {
			log.error("{}PDF下载失败！{}", fileName, e.getMessage());
			throw new JiMuBizException("PDF文件生成失败，请稍候再试");
		} finally {
			document.close();
			try {
				outputStream.close();
			} catch (IOException e) {
				log.info(e.getMessage());
			}
		}
		return Result.newSuccessResult(null);
	}

	/**
	 * 获取当前日期 - 年月日时分秒
	 */
	private String dateNowStr() {
		Date d = new Date();
		System.out.println(d);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		return sdf.format(d);
	}
}
