package org.openoa.engine.bpmnconf.constant;

public class AntFlowConstants {
    /**
     * un approved task when a process is ending
     */
    public final static String UNAPPROVED_TASK = "unapprovedTask";
    /**
     * system process task assigner
     */
    public final static String SYSTEM_PROCESS_TASK_USER = "systemProcessTaskUser";
    /**
     * task assignee
     */
    public final static String ASSIGNEE_JUMP = "assigneeJump";
    /**
     * indicate whether a process started or not
     */
    public final static String HASSTART_NUM = "hasStartNum";
    /**
     * process submit user
     */
    public final static String APPROVAL_USER_CODE = "approvalUserCode";

    /**
     * whether a process is rollback or not
     */
    public final static String IS_ROLLBACK = "rollback";

    /**
     * process starter user
     */
    public final static String START_USER = "startUser";
    /**
     * hrbp
     */
    public final static String HRBP = "HRBP";
    /**
     * HRBP leader
     */
    public final static String HR_LEADER = "HRLeader";
    /**
     * employee
     */
    public final static String STAFF_ID = "staff";
    /**
     * work handover
     */
    public final static String STAFF_HANDOVER = "handover";
    /**
     * end node key
     */
    public final static String END_KEY = "end";
    /**
     * parallel gateway
     */
    public final static String PARALLEL_GATEWAY = "parallelGateway";
    /**
     * executive gateway
     */
    public final static String EXCLUSIVE_GATEWAY = "exclusiveGateway";
    /**
     * jump node key
     */
    public final static String TASK_KEY = "task1418018332279";
    /**
     * start node key
     */
    public final static String START_NODE = "task1418018332271";
    /**
     * inclusive gateway
     */
    public final static String INCLUSIVE_GATEWAY = "inclusiveGateway";

    public final static String USER_TASK = "userTask";

    public final static String END_EVENT = "end";
    public final static String END_PROCESS = "endevent1";

    public final static String SERVICE_TASK = "serviceTask";

    public final static String MAIL_TASK = "mailTask";

    public final static String TURN_AROUND = "turnround";
    public final static String candidateUsers = "candidateUsers";
    //department leader
    public final static String LEADER = "leader";
    public static final String NUM_OPERATOR="numberOperator";
    public static final String DRAWING_FINT="宋体";
    public static final String SCRIPT_CONTEXT="it";

	public static final String BPMN_CONF_DETAIL_CACHE_PREFIX = "Antflow:BpmnConfDetail:";
}
