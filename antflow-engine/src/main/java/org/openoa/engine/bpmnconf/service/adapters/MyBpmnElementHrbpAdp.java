package org.openoa.engine.bpmnconf.service.adapters;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.NodePropertyEnum;
import org.openoa.base.vo.BpmnConfCommonElementVo;
import org.openoa.base.vo.BpmnNodeParamsAssigneeVo;
import org.openoa.base.vo.BpmnNodeParamsVo;
import org.openoa.base.vo.BpmnNodePropertysVo;
import org.openoa.common.util.BpmnElementUtils;
import org.openoa.engine.bpmnconf.service.MyBpmnElementAdaptor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class MyBpmnElementHrbpAdp extends MyBpmnElementAdaptor {

    @Override
    protected BpmnConfCommonElementVo getElementVo(BpmnNodePropertysVo property, BpmnNodeParamsVo params, Integer elementCode, String elementId) {

        BpmnNodeParamsAssigneeVo bpmnNodeParamsAssigneeVo = Optional.ofNullable(params.getAssignee())
                .orElse(new BpmnNodeParamsAssigneeVo());
        String assignee = bpmnNodeParamsAssigneeVo.getAssignee();
        String assigneeName = bpmnNodeParamsAssigneeVo.getAssigneeName();
        Map<String,String> singleAssigneeMap=new HashMap<>();
        singleAssigneeMap.put(assignee,assigneeName);
        return BpmnElementUtils.getSingleElement(elementId, bpmnNodeParamsAssigneeVo.getElementName(),
                StringUtils.join("hrbpUser", elementCode), assignee,singleAssigneeMap);
    }

    @Override
    public void setSupportBusinessObjects() {
        addSupportBusinessObjects(NodePropertyEnum.NODE_PROPERTY_HRBP);
    }
}
