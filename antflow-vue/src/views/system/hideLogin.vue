<template>
	<div class="login-wrap">
    <div class="turn-login">
      <el-select @change="changeLogin" v-model="selectedCorpid" placeholder="请选择登录企微">
        <el-option v-for="(item, index) in wecomList" :key="index" :label="item.corp_name" :value="item.corpid" />
      </el-select>
    </div>
    <div class="account-login">
      <h1>账号登录</h1>
      <el-form :model="userForm" status-icon :rules="userFormRules" ref="userFormRef" class="user-form">
        <el-form-item prop="empId">
          <el-input v-model.number="userForm.empId" :prefix-icon="User" placeholder="请输入员工id"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="loginLoading" @click="empIdLogin">登录</el-button>
        </el-form-item>
      </el-form>
    </div>
	</div>
</template>

<script>
import { defineComponent, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User } from '@element-plus/icons-vue'
import { setToken, setUserInfo } from '@/utils/auth'
import { hideLogin } from '@/api/login'
import { wecomCorpListOnline, wecomCorpListTest } from '@/utils/constants'

export default defineComponent({
  setup() {
    const route = useRoute()
    const router = useRouter()

    const wecomList = window.__ce.ENV === 'prod' ? wecomCorpListOnline : wecomCorpListTest

    let wecomInfo = {
      corpid: '',
      corp_name: '',
      agentid: ''
    }
    const selectedCorpid = ref()
    let loading = ref(true)
    let loginLoading = ref(false)
    let userForm = ref({
      empId: ''
    })
    let userFormRules = {
      empId: [
        { required: true, message: '请输入员工id', trigger: 'blur' }
      ]
    }
    const userFormRef = ref()

    const empIdLogin = () => {
      userFormRef.value?.validate((valid) => {
        if(valid) {
          loginLoading.value = true
          hideLogin({
            empId: userForm.value.empId,
            agentId: wecomInfo.agentid || wecomList[0].agentid
          }).then(res => {
            if(res?.data?.token) {
              setToken(res.data.token)
              delete res.data.token
              setUserInfo(res.data)

              let { redirect } = route.query
              redirect = redirect ? decodeURIComponent(redirect) : ''
              console.log('tokenLogin-redirect: ', redirect)
              let path = redirect || '/index'
              router.replace(path)
            } else {
              ElMessage.error(res?.msg || '登录失败，请稍后再试')
            }
          }).finally(() => {
            loginLoading.value = false
          })
        }
      })
    }

    const changeLogin = (value) => {
      wecomInfo = wecomList.find(item => value === item.corpid) || {
        corpid: '',
        corp_name: '',
        agentid: ''
      }
    }

    const init = () => {
      wecomInfo = wecomList[0]
      selectedCorpid.value = wecomInfo.corpid
    }
    init()
    return {
      empIdLogin,
      changeLogin,
      wecomList,
      selectedCorpid,
      loading,
      userForm,
      userFormRules,
      loginLoading,
      userFormRef,
      User
    }
  }
})
</script>

<style lang="scss" scoped>
.login-wrap {
	width: 100%;
	height: 100vh;
	background: url('/img/login-bg.jpeg') right/cover no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .turn-login {
    width: 480px;
  }
  .account-login {
    width: 480px;
    height: 416px;
    background-color: #fff;
    margin-top: 10px;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .user-form {
      width: 260px;
      :deep(.el-icon) {
        font-size: 16px;
      }
      :deep(.el-input) {
        height: 42px;
      }
      :deep(.el-button) {
        height: 42px;
        width: 100%;
        font-size: 16px;
      }
    }
  }
}
</style>