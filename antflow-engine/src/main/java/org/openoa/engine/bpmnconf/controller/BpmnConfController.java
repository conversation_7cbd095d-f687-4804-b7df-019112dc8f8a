package org.openoa.engine.bpmnconf.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.NodePropertyEnum;
import org.openoa.base.dto.PageDto;
import org.openoa.base.entity.Result;
import org.openoa.base.exception.JiMuBizException;
import org.openoa.base.interf.ActivitiService;
import org.openoa.base.util.SecurityUtils;
import org.openoa.base.vo.*;
import org.openoa.engine.bpmnconf.confentity.BpmnNode;
import org.openoa.engine.bpmnconf.confentity.templategroup.TemplateGroupConf;
import org.openoa.engine.bpmnconf.service.biz.BpmBusinessProcessServiceImpl;
import org.openoa.engine.bpmnconf.service.biz.BpmVerifyInfoBizServiceImpl;
import org.openoa.engine.bpmnconf.service.biz.BpmnConfCommonServiceImpl;
import org.openoa.engine.bpmnconf.service.biz.ProcessApprovalServiceImpl;
import org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl;
import org.openoa.engine.bpmnconf.service.impl.BpmnNodeServiceImpl;
import org.openoa.engine.bpmnconf.service.impl.BpmnNodeToServiceImpl;
import org.openoa.engine.bpmnconf.service.templategroup.impl.TemplateGroupServiceImpl;
import org.openoa.engine.bpmnconf.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Classname BpmnConfController
 * @Description TODO
 * @Date 2022-02-19 16:21
 * @Created by AntOffice
 */

@Slf4j
@RestController
@RequestMapping(value = "/bpmnConf")
public class BpmnConfController {
    @Autowired
    private BpmnConfServiceImpl bpmnConfService;
    @Autowired
    private BpmnNodeToServiceImpl bpmnNodeToService;
    @Autowired
    private ProcessApprovalServiceImpl processApprovalService;
    @Autowired
    private BpmnConfCommonServiceImpl bpmnConfCommonService;
    @Autowired
    private BpmVerifyInfoBizServiceImpl bpmVerifyInfoBizService;
    @Autowired(required = false)
    private Map<String, ActivitiService> activitiServices;
    @Autowired
    private BpmnNodeServiceImpl testService;
	@Autowired
	private BpmBusinessProcessServiceImpl bpmBusinessProcessService;
	@Autowired
	private TemplateGroupServiceImpl templateGroupService;
	@Autowired
	private BpmnNodeServiceImpl bpmnNodeService;

	/**
     * 首页代办统计
     *
     * @return
     */
    @GetMapping("/todoList")
    public Result<TaskMgmtVO> todoList() {
        TaskMgmtVO taskMgmtVO = processApprovalService.processStatistics();
        return Result.newSuccessResult(taskMgmtVO);
    }

    /**
     * 流程设计发布/复制
     *
     * @param bpmnConfVo
     * @return
     */
    @PostMapping("/edit")
    public Result edit(@RequestBody BpmnConfVo bpmnConfVo, HttpServletRequest request) {
	    // BpmnConfVo 这个类有很多地方在用，去掉了@Valid相关的校验注解，改为手动验证
		if (StringUtils.isBlank(bpmnConfVo.getBpmnName())) {
			return Result.newFailureResult("1001", "模板名称不能为空");
		}
		if (Objects.isNull(bpmnConfVo.getTemplateGroupId())) {
			return Result.newFailureResult("1001", "模板分组id不能为空");
		}
		if (StringUtils.isNotBlank(bpmnConfVo.getRemark())) {
			if (bpmnConfVo.getRemark().length() > 200) {
				return Result.newFailureResult("1001", "模板说明最长可输入200个字符");
			}
		}
	    String templateGroupName = Optional.ofNullable(
			    templateGroupService.lambdaQuery().eq(TemplateGroupConf::getId, bpmnConfVo.getTemplateGroupId()).one())
		    .map(TemplateGroupConf::getName).orElse(StringUtils.EMPTY);
		bpmnConfVo.setTemplateGroupName(templateGroupName);
		// 发布即启动
		bpmnConfVo.setEffectiveStatus(1);
	    bpmnConfVo.setUserId(SecurityUtils.getEmpIdJwt(request));
	    bpmnConfService.edit(bpmnConfVo);
        return Result.newSuccessResult("ok");
    }

    /**
     * 流程设计信息列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/listPage")
    public Result<ResultAndPage<BpmnConfVo>> listPage(@RequestBody BpmnConfDetailReqDto dto) {
        PageDto page = dto.getPageDto();
		// 用户搜索模板编号（id字段时）输入中文字符串，会报JSON格式转换错误，这里兼容一下，写一个不存在的id
	    BpmnConfDetailSearchReqDto searchBy = dto.getEntity();
	    BpmnConfVo vo = BeanCopyUtils.convertToVo(searchBy, BpmnConfVo.class);
	    if (Objects.nonNull(searchBy) && StringUtils.isNotBlank(searchBy.getId())) {
			if (StringUtils.isNumeric(searchBy.getId())) {
				vo.setId(Long.parseLong(searchBy.getId()));
			} else {
				vo.setId(-999L);
			}

	    }
	    return Result.newSuccessResult(bpmnConfService.selectPage(page, vo));
    }

    /**
     * 流程设计信息预览详情
     */
    @PostMapping("/preview")
    public Result preview( @RequestBody String params) {
        return Result.newSuccessResult(bpmnConfCommonService.previewNode(params));
    }

    /**
     * 发起业务流程（DIY/LF）
     */
    @PostMapping("/startPagePreviewNode")
    public Result<PreviewNode> startPagePreviewNode(@RequestBody String params) {
        JSONObject jsonObject = JSONObject.parseObject(params);
        Boolean isStartPreview = jsonObject.getBoolean("isStartPreview");

        if (isStartPreview == null || isStartPreview) {
            return Result.newSuccessResult(bpmnConfCommonService.startPagePreviewNode(params, false));
        } else {
            return Result.newSuccessResult(bpmnConfCommonService.taskPagePreviewNode(params));
        }

    }

    /**
     * 根据表单中填写的条件，获取后续的节点
     */
    @PostMapping("/getFollowNodes")
    public Result<PreviewNode> getFollowNodes(@RequestBody String params) {
	    PreviewNode previewNode = bpmnConfCommonService.startPagePreviewNode(params, true);
	    if (!CollectionUtils.isEmpty(previewNode.getBpmnNodeList())) {
		    Long confId = previewNode.getBpmnNodeList().get(0).getConfId();
		    List<BpmnNode> ccNodes = bpmnNodeService.lambdaQuery().eq(BpmnNode::getConfId, confId)
			    .eq(BpmnNode::getCcSelfSelectFlag, 1)
			    .eq(BpmnNode::getNodeProperty, NodePropertyEnum.NODE_PROPERTY_CUSTOMIZE.getCode())
			    .list();
		    Set<String> nodeFromSet = previewNode.getBpmnNodeList().stream().map(BpmnNodeVo::getNodeFrom).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
		    Set<String> nodeFromsSet = previewNode.getBpmnNodeList().stream().map(BpmnNodeVo::getNodeFroms).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
		    Set<String> nodeToSet = previewNode.getBpmnNodeList().stream().filter(node ->  Objects.nonNull(node.getNodeTo())).flatMap(x -> x.getNodeTo().stream()).collect(Collectors.toSet());
		    Set<String> allConditionNodeId = new HashSet<>();
		    allConditionNodeId.addAll(nodeFromSet);
		    allConditionNodeId.addAll(nodeFromsSet);
		    allConditionNodeId.addAll(nodeToSet);
		    if (!CollectionUtils.isEmpty(ccNodes)) {
			    List<BpmnNodeVo> ccSelfNodes = BeanCopyUtils.convertToVoList(ccNodes, BpmnNodeVo.class);
			    List<BpmnNodeVo> ccConditionNodes = ccSelfNodes.stream().filter(x -> allConditionNodeId.contains(x.getNodeId()))
				    .sorted(Comparator.comparing(BpmnNodeVo::getId).reversed()).collect(Collectors.toList());
			    if (!CollectionUtils.isEmpty(ccConditionNodes)) {
				    previewNode.getBpmnNodeList().addAll(ccConditionNodes);
			    }
		    }
		    Map<String, BpmnNodeVo> nodeMap = previewNode.getBpmnNodeList().stream().collect(Collectors.toMap(BpmnNodeVo::getNodeId, node -> node, (n1, n2) -> n1));
		    BpmnNodeVo startNode = nodeMap.get("Gb2");
		    if (startNode == null) {
				log.warn("getFollowNodes has no start node, previewNode.getBpmnNodeList()={}", JSON.toJSONString(previewNode.getBpmnNodeList()));
			    return Result.newSuccessResult(previewNode);
		    }
			// 此时previewNode.getBpmnNodeList()已经包含了自选抄送人信息，将node按照nodeFrom、nodeId、nodeTo的顺序连接起来
		    List<BpmnNodeVo> orderedNodes = new ArrayList<>();
		    Set<String> visitedNodeIds = new HashSet<>();
		    Queue<BpmnNodeVo> nodeQueue = new LinkedList<>();

		    // 将起始节点加入队列和结果列表
		    nodeQueue.offer(startNode);
		    visitedNodeIds.add(startNode.getNodeId());
		    orderedNodes.add(startNode);

		    // BFS遍历节点
		    while (!nodeQueue.isEmpty()) {
			    BpmnNodeVo currentNode = nodeQueue.poll();
			    // 处理当前节点的所有后继节点(nodeTo)
			    if (currentNode.getNodeTo() != null) {
				    for (String nextNodeId : currentNode.getNodeTo()) {
					    if (nodeMap.containsKey(nextNodeId) && !visitedNodeIds.contains(nextNodeId)) {
						    BpmnNodeVo nextNode = nodeMap.get(nextNodeId);
						    nodeQueue.offer(nextNode);
						    visitedNodeIds.add(nextNodeId);
						    orderedNodes.add(nextNode);
					    }
				    }
			    }
		    }
		    for (BpmnNodeVo node : previewNode.getBpmnNodeList()) {
			    if (!visitedNodeIds.contains(node.getNodeId())) {
				    orderedNodes.add(node);
				    visitedNodeIds.add(node.getNodeId());
			    }
		    }
		    // 更新节点列表为排序后的结果
		    previewNode.setBpmnNodeList(orderedNodes);
	    }
	    return Result.newSuccessResult(previewNode);
    }

    /**
     * 获取审批进度数据信息
     *
     * @param processNumber
     * @return
     */
    @GetMapping("/getBpmVerifyInfoVos")
    public Result<List<BpmVerifyInfoVo>> getBpmVerifyInfoVos(@RequestParam("processNumber") String processNumber) {
        return Result.newSuccessResult(bpmVerifyInfoBizService.getBpmVerifyInfoVos(processNumber, false));
    }

    /**
     * 获取审批页面按钮权限
     *
     * @param values
     * @param formCode
     * @return
     */
    @PostMapping("/process/viewBusinessProcess")
    public Result<BusinessDataVo> viewBusinessProcess(@RequestBody String values, String formCode) {
        return Result.newSuccessResult(processApprovalService.getBusinessInfo(values, formCode));
    }

    /**
     * 审批页：审批,发起，重新提交 operationType 1 发起 2 重新提交 3 审批
     *
     * @param values
     * @param formCode
     * @return
     */
    @PostMapping("/process/buttonsOperation")
    public Result buttonsOperation(@RequestBody String values, String formCode, HttpServletRequest request) {
        BusinessDataVo resultData = processApprovalService.buttonsOperation(values, formCode, request);
        return Result.newSuccessResult(resultData);
    }

    /**
     * 流程设计列表：启用
     *
     * @param id
     * @return
     */
    @Deprecated
	@GetMapping("/effectiveBpmn/{id}")
    public Result effectiveBpmn(@PathVariable("id") Integer id) {
        bpmnConfService.effectiveBpmnConf(id);
        return Result.newSuccessResult(null);
    }

    /**
     * 流程设计详情
     *
     * @param id
     * @return
     */
    @RequestMapping("/detail/{id}")
    public Result<BpmnConfVo> detail(@PathVariable("id") Integer id) {
        return Result.newSuccessResult(bpmnConfService.detail(id));
    }


    /**
     * 根据ID删除一个版本
     * 入参只传递ID即可
     */
    @PostMapping("/delete")
    public Result<?> delete(@RequestBody IdVo vo) {
        if(vo == null){
            throw new JiMuBizException("invalid vo");
        }
        bpmnConfService.deleteByBpmnConfId(vo.getId());
        return Result.newSuccessResult("ok");
    }



    /**
     * 流程列表  3我的发起，4我的代办，5我的已办，6所有进行中实例，9抄送到我
     *
     * @param requestDto
     * @param type
     * @return
     * @throws JiMuBizException
     */
    @RequestMapping("/process/listPage/{type}")
    public ResultAndPage<TaskMgmtVO> viewPcProcessList(@RequestBody DetailRequestDto requestDto, @PathVariable("type") Integer type) throws JiMuBizException {
        PageDto pageDto = requestDto.getPageDto();
        TaskMgmtVO taskMgmtVO = requestDto.getTaskMgmtVO();
        taskMgmtVO.setType(type);
        return processApprovalService.findPcProcessList(pageDto, taskMgmtVO);
    }


	/**
	 * 发起人的表单权限信息
	 */
	@GetMapping("/process/perm/{confId}")
	public Result<List<LFFieldControlVO>> getFieldControlListOfSponsor(@PathVariable("confId") Long confId) {
		return bpmnConfService.getFieldControlListOfSponsor(confId);
	}

	/**
	 * 修改模板数据权限
	 */
	@PostMapping("/visible/modifyPerm")
	public Result<Boolean> updateBpmnConfPerm(@Valid @RequestBody AddBpmnConfPermReqDto req) {
		return bpmnConfService.updateBpmnConfPerm(req);
	}

	/**
	 * 查询模板的可见范围
	 */
	@GetMapping("/visible/perm/{confId}")
	public Result<List<BpmnConfUserPermResVo>> findBpmnConfPerm(@Valid @PathVariable("confId") Long confId) {
		return bpmnConfService.findBpmnConfPerm(confId);
	}

	/**
	 * 修改标签的数据权限
	 */
	@PostMapping("userTagPerm/modifyPerm")
	public Result<Boolean> modifyUserTagPerm(@Valid @RequestBody AddUserTagPermReqDto req) {
		return Result.newSuccessResult(bpmnConfService.modifyUserTagPerm(req));
	}

	/**
	 * 查询用户标签的可见范围
	 */
	@GetMapping("userTagPerm/{roleId}")
	public Result<List<BpmnConfUserPermResVo>> findUserTagPerm(@PathVariable("roleId") Long roleId) {
		return Result.newSuccessResult(bpmnConfService.findUserTagPerm(roleId));
	}

	/**
	 * 手动让流程终结，并且删除掉此流程 （仅用于删除系统中的无效流程）
	 */
	@PostMapping("makeProcessEnd")
	public Result<Void> makeProcessEnd(@RequestBody MakeProcessEndReqVo req) {
		bpmBusinessProcessService.makeProcessEnd(req);
		return Result.newSuccessResult(null);
	}

	/**
	 * 提交人流程催办
	 */
	@GetMapping("submiterReminders")
	public Result<Void> submiterReminders(@RequestParam String businessId) {
		bpmBusinessProcessService.submiterReminders(businessId);
		return Result.newSuccessResult(null);
	}
}
