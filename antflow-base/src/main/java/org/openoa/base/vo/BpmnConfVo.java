package org.openoa.base.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import java.util.Date;
import java.util.List;

/**
 * @Classname BpmnConfVo
 * @Description conf vo
 * @since 0.5
 * @Created by AntOffice
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BpmnConfVo {

    /**
     * auto incr Id
     */
    private Long id;
    /**
     * bpmnCode
     */
    private String bpmnCode;
    /**
     * bpmnName
     */
    private String bpmnName;
    /**
     * process's type
     */
    private Integer bpmnType;
    /**
     * formCode
     */
    private String formCode;
    /**
     * formCode
     */
    private String formCodeDisplayName;
    /**
     * appId
     */
    private Integer appId;
    /**
     */
    private Integer deduplicationType;
    /**
     */
    private Integer effectiveStatus;
    /**
     */
    private Integer isAll;

    /**
     */
    private Integer isOutSideProcess;
    private Integer isLowCodeFlow;
    /**
     * process's business party
     */
    private Long businessPartyId;

    /**
     * process's business party
     */
    private Integer applicationId;


    private Integer extraFlags;
    /**
     * 流程说明
     */
    private String remark;
    /**
     */
    private Integer isDel;
    /**
     */
    private String createUser;
    /**
     */
    private Date createTime;
    /**
     */
    private String updateUser;
    /**
     */
    private Date updateTime;

    //===============>>query to do list<<===================

    /**
     * searh condition
     */
    private String search;

    //===============>>extend info<<===================

    /**
     */
    private String deduplicationTypeName;

    /**
     */
    private String createUserName;

    /**
     */
    private String createUserUuid;

    /**
     */
    private String businessPartyName;


    private String businessPartyMark;

    /**
     * conf's nodes
     */
    private List<BpmnNodeVo> nodes;

    /**
     * buttons on view page
     */
    private BpmnViewPageButtonBaseVo viewPageButtons;

    /**
     * out of scope notice template
     */
    private List<BpmnTemplateVo> templateVos;

    //===============>>thirdy party process<<===================

    /**
     * form json
     */
    private String formData;

    /**
     * process's conf callback
     */
    private String bpmConfCallbackUrl;

    /**
     * process's flow callback
     */
    private String bpmFlowCallbackUrl;

    /**
     * where to view the process
     */
    private String viewUrl;

    /**
     * submit url
     */
    private String submitUrl;

    /**
     * process condition url
     */
    private String conditionsUrl;


    private List<Long> businessPartyIds;

    /**
     * business type 1 for embedded 2 for api access
     */
    private Integer type;
    private String lfFormData;
    private Long lfFormDataId;

	/**
	 * 模板id
	 */
	private String templateId;

	/**
	 * 模板名称
	 */
	@Max(value = 20, message = "模板名称最长可输入20个字符")
	private String templateName;

	/**
	 * 模板分组id
	 */
	private Long templateGroupId;

	/**
	 * 模板分组名称
	 */
	private String templateGroupName;

	/**
	 * 用户Id
	 */
	private String userId;

	/**
	 * 选择可见范围的类型 1-人员 2-部门
	 */
	private Integer rangeType;


	/**
	 * 模板分组id列表（用来筛选模板列表的分组，支持多选）
	 */
	private List<Long> templateGroupIds;

	/**
	 * 表单中的提交人
	 */
	private String submitUserFieldId;
}