package org.openoa.engine.bpmnconf.service.biz;

import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.TaskService;
import org.openoa.base.constant.enums.ProcessOperationEnum;
import org.openoa.base.interf.ProcessOperationAdaptor;
import org.openoa.base.mapper.UserMapper;
import org.openoa.base.util.SecurityUtils;
import org.openoa.base.vo.BusinessDataVo;
import org.openoa.engine.bpmnconf.mapper.TaskMgmtMapper;
import org.openoa.engine.bpmnconf.service.impl.BpmFlowrunEntrustServiceImpl;
import org.openoa.engine.bpmnconf.util.UserMsgUtils;
import org.openoa.engine.factory.FormFactory;
import org.openoa.engine.factory.IAdaptorFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

import static org.openoa.base.constant.enums.CallbackTypeEnum.*;

/**
 *<AUTHOR>
 * @Description button operation servicve
 * @Param
 * @return
 * @Version 0.5
 */
@Service
@Slf4j
public class ButtonOperationServiceImpl{
    @Autowired
    private IAdaptorFactory adaptorFactory;
    @Autowired
    private ThirdPartyCallBackServiceImpl thirdPartyCallBackService;
	@Autowired
	private UserMapper userMapper;
	@Autowired
	protected BpmBusinessProcessServiceImpl processService;
	@Autowired
	protected BpmBusinessProcessServiceImpl bpmBusinessProcessService;
	@Autowired
	private TaskService taskService;
	@Autowired
	private TaskMgmtMapper taskMgmtMapper;
	@Autowired
	private BpmFlowrunEntrustServiceImpl bpmFlowrunEntrustService;
	@Autowired
	private FormFactory formFactory;
	@Autowired
	private BpmProcessNameServiceImpl bpmProcessNameService;
	@Autowired
	private BpmnConfCommonServiceImpl bpmnConfCommonService;
	@Resource
	private ResubmitProcessImpl resubmitProcessImpl;
    @Transactional(rollbackFor = Exception.class)
    public BusinessDataVo buttonsOperationTransactional(BusinessDataVo vo) {

        //Do button operations
        ProcessOperationAdaptor processOperation = adaptorFactory.getProcessOperation(vo);
        try {
            processOperation.doProcessButton(vo);
            if (vo.getIsOutSideAccessProc()) {

                String verifyUserName = SecurityUtils.getLogInEmpNameSafe();

                String verifyUserId = SecurityUtils.getLogInEmpIdSafe();
//                Map<String, Object> objectMap = vo.getObjectMap();
//                if (!CollectionUtils.isEmpty(objectMap)) {
//                    verifyUserName = Optional.ofNullable(objectMap.get("employeeName")).map(String::valueOf).orElse(StringUtils.EMPTY);
//                    verifyUserId = Optional.ofNullable(objectMap.get("employeeId")).map(Object::toString).orElse("");
//                }
                ProcessOperationEnum poEnum = ProcessOperationEnum.getEnumByCode(vo.getOperationType());
                switch (Objects.requireNonNull(poEnum)){
                    case BUTTON_TYPE_SUBMIT:
                        thirdPartyCallBackService.doCallback(vo.getBpmFlowCallbackUrl(), PROC_STARTED_CALL_BACK, vo.getBpmnConfVo(),
                                vo.getProcessNumber(), vo.getBusinessId(),verifyUserName);
                    case BUTTON_TYPE_AGREE:
                        thirdPartyCallBackService.doCallback(vo.getBpmFlowCallbackUrl(), PROC_COMMIT_CALL_BACK, vo.getBpmnConfVo(),
                                vo.getProcessNumber(), vo.getBusinessId(),verifyUserName);
                        break;
                    case BUTTON_TYPE_ABANDON:
                        thirdPartyCallBackService.doCallback(vo.getBpmFlowCallbackUrl(), PROC_END_CALL_BACK, vo.getBpmnConfVo(),
                                vo.getProcessNumber(), vo.getBusinessId(),verifyUserName);
                        break;
                }
            }
			// 消息处理 t_user_message
	        String userId = SecurityUtils.getLogInEmpIdStr();

	        UserMsgUtils.updateMsgStatus(userId, vo.getFormCode() + "_" + vo.getBusinessId());

        } catch (Exception e){
            throw e;
        }
        return vo;

    }

}
