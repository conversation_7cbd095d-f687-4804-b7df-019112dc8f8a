package org.openoa.engine.bpmnconf.service.adapters;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.NodePropertyEnum;
import org.openoa.base.constant.enums.SignTypeEnum;
import org.openoa.base.vo.BpmnConfCommonElementVo;
import org.openoa.base.vo.BpmnNodeParamsAssigneeVo;
import org.openoa.base.vo.BpmnNodeParamsVo;
import org.openoa.base.vo.BpmnNodePropertysVo;
import org.openoa.common.util.BpmnElementUtils;
import org.openoa.engine.bpmnconf.service.MyBpmnElementAdaptor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MyBpmnElementRoleAdp extends MyBpmnElementAdaptor {

    @Override
    protected BpmnConfCommonElementVo getElementVo(BpmnNodePropertysVo property, BpmnNodeParamsVo params, Integer elementCode, String elementId) {


        String elementName = params.getAssigneeList().get(0).getElementName();
        Map<String, String> assigneeMap = params.getAssigneeList()
                .stream()
                .filter(o -> o.getIsDeduplication() == 0)
                .collect(Collectors.toMap(BpmnNodeParamsAssigneeVo::getAssignee, BpmnNodeParamsAssigneeVo::getAssigneeName, (k1, k2) -> k1));
        if (property.getSignType().equals(SignTypeEnum.SIGN_TYPE_SIGN.getCode())) {
            return BpmnElementUtils.getMultiplayerSignElement(elementId, elementName,
                    StringUtils.join("roleUserList", elementCode),new ArrayList<>(assigneeMap.keySet()),assigneeMap);
        } else {
            return BpmnElementUtils.getMultiplayerOrSignElement(elementId, elementName,
                    StringUtils.join("roleUserList", elementCode), new ArrayList<>(assigneeMap.keySet()),assigneeMap);
        }

    }

    @Override
    public void setSupportBusinessObjects() {
        addSupportBusinessObjects(NodePropertyEnum.NODE_PROPERTY_ROLE);
    }
}
