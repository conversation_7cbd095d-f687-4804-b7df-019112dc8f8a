package org.openoa.engine.bpmnconf.service.templategroup;

import org.openoa.base.vo.ResultAndPage;
import org.openoa.engine.vo.templategroup.req.BpmApproveTemplateGroupReq;
import org.openoa.engine.vo.templategroup.resp.BpmApproveTemplateGroupResp;

/**
 * @Description 模板分组管理
 * <AUTHOR>
 * @Date 2025-04-17 09:24
 */
public interface TemplateGroupService {

	/**
	 * 获取模板分组分页列表
	 */
	ResultAndPage<BpmApproveTemplateGroupResp> listPage(BpmApproveTemplateGroupReq req);
}
