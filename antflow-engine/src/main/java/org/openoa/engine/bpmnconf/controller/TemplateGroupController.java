package org.openoa.engine.bpmnconf.controller;

import lombok.extern.slf4j.Slf4j;
import org.openoa.base.vo.ResultAndPage;
import org.openoa.engine.bpmnconf.service.templategroup.impl.TemplateGroupServiceImpl;
import org.openoa.engine.vo.templategroup.req.BpmApproveTemplateGroupReq;
import org.openoa.engine.vo.templategroup.resp.BpmApproveTemplateGroupResp;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value = "/templateGroup")
@Validated

public class TemplateGroupController {

    @Resource
    private TemplateGroupServiceImpl templateGroupService;


	/**
	 * 获取模板分组
	 * @param req 请求参数
	 * @return 返回模板分组
	 */
	@PostMapping("/listPage")
	public ResultAndPage<BpmApproveTemplateGroupResp> approveTemplateListPage(@RequestBody BpmApproveTemplateGroupReq req) {
		return templateGroupService.listPage(req);
	}

}
