package org.openoa.engine.bpmnconf.service.templategroup.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.YesOrNoEnum;
import org.openoa.base.util.PageUtils;
import org.openoa.base.vo.ResultAndPage;
import org.openoa.engine.bpmnconf.confentity.templategroup.TemplateGroupConf;
import org.openoa.engine.bpmnconf.mapper.templategroup.BpmTemplateGroupMapper;
import org.openoa.engine.bpmnconf.util.BeanCopyUtils;
import org.openoa.engine.vo.templategroup.req.BpmApproveTemplateGroupReq;
import org.openoa.engine.vo.templategroup.resp.BpmApproveTemplateGroupResp;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模板分组实现类
 */
@Service
@Slf4j
public class TemplateGroupServiceImpl extends ServiceImpl<BpmTemplateGroupMapper, TemplateGroupConf> {

	/**
	 * 获取模板分组分页列表
	 */
	public ResultAndPage<BpmApproveTemplateGroupResp> listPage(BpmApproveTemplateGroupReq req) {
		Page<TemplateGroupConf> page = PageUtils.getPageByPageDto(req.getPageDto());
		Page<TemplateGroupConf> pageList = lambdaQuery()
			.eq(TemplateGroupConf::getDeleted, YesOrNoEnum.NO.getCode())
			.like(StringUtils.isNotBlank(req.getName()), TemplateGroupConf::getName, req.getName())
			.page(page);
		List<BpmApproveTemplateGroupResp> rs = BeanCopyUtils.convertToVoList(pageList.getRecords(), BpmApproveTemplateGroupResp.class);
		Page<BpmApproveTemplateGroupResp> templateGroupPage = new Page<>(pageList.getCurrent(), pageList.getSize(), pageList.getTotal());
		templateGroupPage.setRecords(rs);
		return PageUtils.getResultAndPage(templateGroupPage);
	}

}


