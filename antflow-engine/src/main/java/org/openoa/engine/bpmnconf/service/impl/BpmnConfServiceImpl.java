package org.openoa.engine.bpmnconf.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.*;
import org.openoa.base.dto.PageDto;
import org.openoa.base.entity.BpmBusinessProcess;
import org.openoa.base.entity.Employee;
import org.openoa.base.entity.Result;
import org.openoa.base.exception.JiMuBizException;
import org.openoa.base.mapper.UserMapper;
import org.openoa.base.service.ProcessorFactory;
import org.openoa.base.service.empinfoprovider.BpmnEmployeeInfoProviderService;
import org.openoa.base.util.*;
import org.openoa.base.vo.*;
import org.openoa.engine.bpmnconf.adp.bpmnnodeadp.BpmnNodeAdaptor;
import org.openoa.engine.bpmnconf.common.NodeAdditionalInfoServiceImpl;
import org.openoa.engine.bpmnconf.common.TaskMgmtServiceImpl;
import org.openoa.engine.bpmnconf.confentity.*;
import org.openoa.engine.bpmnconf.confentity.templategroup.TemplateGroupConf;
import org.openoa.engine.bpmnconf.constant.AntFlowConstants;
import org.openoa.engine.bpmnconf.constant.enus.BpmnNodeAdpConfEnum;
import org.openoa.engine.bpmnconf.constant.enus.EventTypeEnum;
import org.openoa.engine.bpmnconf.mapper.BpmnConfMapper;
import org.openoa.engine.bpmnconf.mapper.BpmnNodeLfFormdataFieldControlMapper;
import org.openoa.engine.bpmnconf.service.TBpmnMemberControlService;
import org.openoa.engine.bpmnconf.service.biz.BpmBusinessProcessServiceImpl;
import org.openoa.engine.bpmnconf.service.biz.BpmNodeLabelsServiceImpl;
import org.openoa.engine.bpmnconf.service.biz.BpmProcessNameServiceImpl;
import org.openoa.engine.bpmnconf.service.biz.BpmnViewPageButtonBizServiceImpl;
import org.openoa.engine.bpmnconf.service.templategroup.impl.TemplateGroupServiceImpl;
import org.openoa.engine.bpmnconf.util.BeanCopyUtils;
import org.openoa.engine.factory.IAdaptorFactory;
import org.openoa.engine.vo.BpmProcessAppApplicationVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static org.openoa.base.constant.NumberConstants.BPMN_FLOW_TYPE_OUTSIDE;
import static org.openoa.base.constant.enums.NodeTypeEnum.NODE_TYPE_APPROVER;


/**
 * @Classname BpmnConfServiceImpl
 * @Description bpmn conf service
 * @Date 2021-10-31 10:29
 * @Created by AntOffice
 */
@Service
public class BpmnConfServiceImpl extends ServiceImpl<BpmnConfMapper, BpmnConf> {

    private static final String linkMark = "_";
    private static final String LABEL_NAME = "提交人";
	private static final Logger log = LoggerFactory.getLogger(BpmnConfServiceImpl.class);
    @Autowired
    private BpmnNodeServiceImpl bpmnNodeService;
    @Autowired
    private BpmnViewPageButtonServiceImpl bpmnViewPageButtonService;
    @Autowired
    private BpmnNodeToServiceImpl bpmnNodeToService;
    @Autowired
    private BpmnTemplateServiceImpl bpmnTemplateService;
    @Autowired
    private InformationTemplateServiceImpl informationTemplateService;
    @Autowired
    private BpmnNodeButtonConfServiceImpl bpmnNodeButtonConfService;
    @Autowired
    private BpmnNodeSignUpConfServiceImpl bpmnNodeSignUpConfService;
    @Autowired
    private BpmnApproveRemindServiceImpl bpmnApproveRemindService;
    @Autowired
    private BpmnConfNoticeTemplateServiceImpl bpmnConfNoticeTemplateService;
    @Autowired
    private BpmnViewPageButtonBizServiceImpl bpmnViewPageButtonBizService;
    @Autowired
    private BpmProcessNameServiceImpl bpmProcessNameService;
    @Autowired
    private BpmnEmployeeInfoProviderService employeeInfoProvider;
    @Autowired
    private OutSideBpmBusinessPartyServiceImpl outSideBpmBusinessPartyService;
    @Autowired
    private OutSideBpmCallbackUrlConfServiceImpl outSideBpmCallbackUrlConfService;
    @Autowired
    private ApplicationServiceImpl applicationService;
    @Autowired
    private IAdaptorFactory adaptorFactory;
    @Autowired
    private BpmnNodeLfFormdataFieldControlServiceImpl nodeLfFormdataFieldControlService;
    @Autowired
    private BpmNodeLabelsServiceImpl nodeLabelsService;
    @Autowired
    private BpmProcessAppApplicationServiceImpl bpmProcessAppApplicationService;
    @Autowired
    private TaskMgmtServiceImpl TaskMgmtService;
    @Autowired
    private NodeAdditionalInfoServiceImpl nodeAdditionalInfoService;
    @Autowired
    private BpmBusinessProcessServiceImpl bpmBusinessProcessService;
	@Autowired
	private BpmnConfMapper bpmnConfMapper;
	@Autowired
	private TemplateGroupServiceImpl templateGroupServiceImpl;

    @Autowired
    private TBpmnMemberControlService tBpmnMemberControlService;
	@Autowired
	private UserMapper userMapper;
	@Autowired
	private BpmnNodeLfFormdataFieldControlMapper nodeLfFormdataFieldControlMapper;
	@Autowired
	private BpmnConfUserPermServiceImpl bpmnConfUserPermService;
	@Autowired
	private UserTagPermServiceImpl userTagPermService;
	@Autowired
	private StringRedisTemplate redisTemplate;

	/**
	 * 更新模板信息
	 * @param reqDto 更新模板信息
	 */
	@Transactional
	@Deprecated
	public void update(BpmnConfUpdateReqDto reqDto) {
		log.info("针对现有的模板进行更新：「{}」", reqDto.getBpmnConfName());
		// 模板配置表更新
		updateBpmnConf(reqDto);
		// 流程节点更新
		updateBpmnNodes(reqDto);
		// 表单更新

	}

	/**
	 * 表单节点设计更新
	 * @param reqDto
	 */
	public void updateBpmnNodes(BpmnConfUpdateReqDto reqDto) {
		List<BpmnNodeVo> confNodes = reqDto.getNodes();
		int hasStartUserChooseModules = 0;
		int hasCopy = 0;
		int hasLastNodeCopy = 0;
		Integer isOutSideProcess = reqDto.getIsLowCodeFlow();
		Integer isLowCodeFlow = reqDto.getIsLowCodeFlow();
		Long confId = reqDto.getTemplateId();
		for (BpmnNodeVo bpmnNodeVo : confNodes) {
			Long nodeId = bpmnNodeVo.getId();
			if (nodeId == null) {
				throw new JiMuBizException("更新模板节点时，节点id不能为空！");
			}
			if (Objects.equals(NODE_TYPE_APPROVER.getCode(), bpmnNodeVo.getNodeType()) && ObjectUtils.isEmpty(bpmnNodeVo.getNodeProperty())) {
				throw new JiMuBizException("approval node has no property, can not be updated！");
			}

			// if (NodePropertyEnum.NODE_PROPERTY_CUSTOMIZE.getCode().equals(bpmnNodeVo.getNodeProperty())){
			// 	hasStartUserChooseModules = BpmnConfFlagsEnum.HAS_STARTUSER_CHOOSE_MODULES.getCode();
			// }
			// if (NodeTypeEnum.NODE_TYPE_COPY.getCode().equals(bpmnNodeVo.getNodeType())) {
			// 	hasCopy = BpmnConfFlagsEnum.HAS_COPY.getCode();;
			// }
			bpmnNodeVo.setIsOutSideProcess(isOutSideProcess);
			bpmnNodeVo.setIsLowCodeFlow(isLowCodeFlow);

			// if the node has no property,the node property default is "1-no property"
			bpmnNodeVo.setNodeProperty(Optional.ofNullable(bpmnNodeVo.getNodeProperty()).orElse(1));

			BpmnNode bpmnNode = bpmnNodeService.getById(nodeId);
			BeanUtils.copyProperties(bpmnNodeVo, bpmnNode);
			bpmnNodeService.getBaseMapper().updateById(bpmnNode);

			Long bpmnNodeId = bpmnNode.getId();
			if (bpmnNodeId == null) {
				throw new JiMuBizException("can not get bpmn node id!");
			}

			// update node_to
			bpmnNodeToService.updateNodeTo(bpmnNodeVo, bpmnNodeId);

			// update node's button conf
			bpmnNodeButtonConfService.updateButtons(bpmnNodeVo, bpmnNodeId);

			// update node sign up
			bpmnNodeSignUpConfService.updateSignUpConf(bpmnNodeVo, bpmnNodeId);


			bpmnNodeVo.setId(bpmnNodeId);
			bpmnNodeVo.setConfId(confId);
			BpmnNodeAdpConfEnum bpmnNodeAdpConfEnum = NodeAdditionalInfoServiceImpl.getBpmnNodeAdpConfEnum(bpmnNodeVo);

			// if it can not get the node's adapter,continue
			if (ObjectUtils.isEmpty(bpmnNodeAdpConfEnum)) {
				continue;
			}

			// edit in node notice template
			bpmnTemplateService.updateBpmnTemplate(bpmnNodeVo);


			// edit in node approver remind conf
			bpmnApproveRemindService.updateBpmnApproveRemind(bpmnNodeVo);

/*			// get node adaptor
			BpmnNodeAdaptor bpmnNodeAdaptor = nodeAdditionalInfoService.getBpmnNodeAdaptor(bpmnNodeAdpConfEnum);

			// then edit the node
			bpmnNodeAdaptor.editBpmnNode(bpmnNodeVo);
			if(NodeTypeEnum.NODE_TYPE_COPY.getCode().equals(bpmnNodeVo.getNodeType())&&CollectionUtils.isEmpty(bpmnNodeVo.getNodeTo())){
				hasLastNodeCopy = BpmnConfFlagsEnum.HAS_LAST_NODE_COPY.getCode();
			}*/

		}
	}


	/**
	 * 更新模板配置表
	 */
	public void updateBpmnConf(BpmnConfUpdateReqDto reqDto) {
		BpmnConf one = this.lambdaQuery().eq(BpmnConf::getId, reqDto.getTemplateId()).last("limit 1").one();
		Assert.notNull(one, "当前模板" + reqDto.getBpmnConfName() +"不存在");
		one.setBpmnName(reqDto.getBpmnConfName());
		TemplateGroupConf templateGroupConf = templateGroupServiceImpl.lambdaQuery()
			.eq(TemplateGroupConf::getId, reqDto.getTemplateGroupId()).last("limit 1").one();
		Assert.notNull(templateGroupConf, "当前模板组" + reqDto.getTemplateGroupId() + "不存在");
		one.setTemplateGroupId(reqDto.getTemplateGroupId());
		one.setTemplateGroupName(templateGroupConf.getName());
		one.setRemark(reqDto.getRemark());
		updateById(one);
	}

	@Transactional
    public void edit(BpmnConfVo bpmnConfVo) {
        String bpmnName = bpmnConfVo.getBpmnName();

        // 获取前端传过来的bpmnCode，用于判断是否是复制模版
        String oriBpmnCode = bpmnConfVo.getBpmnCode();

        String bpmnCode = getBpmnCode(bpmnName);
        String formCode = generateFormCode();
        BpmnConf bpmnConf = new BpmnConf();
        BeanUtils.copyProperties(bpmnConfVo, bpmnConf);

		// 解析lfFormData中的提交人
		String lfFormData = bpmnConfVo.getLfFormData();
		if (StringUtils.isNotBlank(lfFormData)) {
			JSONArray widgetList = JSON.parseObject(lfFormData).getJSONArray("widgetList");
			int submitterCount = 0;
			for (Object widget : widgetList) {
				JSONObject widgetObj = JSON.parseObject(widget.toString());
				String type = widgetObj.getString("type");
				// 成员控件和文本控件需要解析提交人
				if (Objects.equals("member", type) || Objects.equals("input", type)) {
					Boolean isSubmitter = widgetObj.getJSONObject("options").getBoolean("isSubmitter");
					if (null != isSubmitter && isSubmitter) { // 如果isSubmitter传了true,则说明是提交人控件，提交表单的时候需要解析提交人
						submitterCount++;
						String fieldId = widgetObj.getString("id");
						log.info("创建了一个包含提交人=true的审批模板，提交人的field_id={}", fieldId);
						bpmnConf.setSubmitUserFieldId(fieldId);
						break;
					}
				}

			}
			if (submitterCount != 1) {
				log.warn("表单中“姓名 (列表) ”表单项有且只能有一个, 请检查并设置，bpmnName={}，submitterCount={}", bpmnName, submitterCount);
				throw new IllegalArgumentException("表单中“姓名 (列表) ”表单项有且只能有一个, 请检查并设置");
			}
		}

        bpmnConf.setBpmnCode(bpmnCode);
        bpmnConf.setFormCode(formCode);
		String userId = bpmnConfVo.getUserId();
		Employee user = userMapper.getEmployeeDetailById(userId);
		String userName = Optional.ofNullable(user).map(Employee::getUsername).orElse(null);
		bpmnConf.setCreateUser(userName);
        bpmnConf.setCreateTime(new Date());
        bpmnConf.setUpdateUser(userName);
        bpmnConfVo.setUpdateTime(new Date());
        this.getBaseMapper().insert(bpmnConf);
        BpmnConf currentBpmnConf = this.getBaseMapper().getById(bpmnConf.getId());

        //effectiveBpmnConf(bpmnConf.getId().intValue());
        //notice template
        bpmnConfNoticeTemplateService.insert(bpmnCode);
        Long confId = bpmnConf.getId();
        if(confId==null){
            throw new JiMuBizException(Strings.lenientFormat("conf id for formcode:%s can not be null",formCode));
        }
        bpmnConfVo.setId(confId);
        bpmnViewPageButtonBizService.editBpmnViewPageButton(bpmnConfVo, confId);

        bpmnTemplateService.editBpmnTemplate(bpmnConfVo, confId);

        Integer isOutSideProcess = bpmnConfVo.getIsOutSideProcess();
        Integer isLowCodeFlow = bpmnConfVo.getIsLowCodeFlow();

        ProcessorFactory.executePreWriteProcessors(bpmnConfVo);
        List<BpmnNodeVo> confNodes = bpmnConfVo.getNodes();
        int hasStartUserChooseModules=0;
        int hasStartUserChooseModulesCC=0;
        int hasCopy=0;
        int hasLastNodeCopy=0;

        for (BpmnNodeVo bpmnNodeVo : confNodes) {
            if (bpmnNodeVo.getNodeType().intValue() == NODE_TYPE_APPROVER.getCode()
                    && ObjectUtils.isEmpty(bpmnNodeVo.getNodeProperty())) {
                throw new JiMuBizException("apporver node has no property,can not be saved！");
            }

            Integer oriNodeProperty = bpmnNodeVo.getNodeProperty();

            // 复用原有的NodeProperty
            if (NodePropertyEnum.NODE_PROPERTY_MEMBER_CONTROL.getCode().equals(oriNodeProperty)){
                bpmnNodeVo.setNodeProperty(NodePropertyEnum.NODE_PROPERTY_CUSTOMIZE.getCode());
            }

            if(NodePropertyEnum.NODE_PROPERTY_CUSTOMIZE.getCode().equals(oriNodeProperty)){
                hasStartUserChooseModules=BpmnConfFlagsEnum.HAS_STARTUSER_CHOOSE_MODULES.getCode();
            }
	        if(Objects.equals(bpmnNodeVo.getCcSelfSelectFlag(), 1)) {
		        hasStartUserChooseModulesCC = BpmnConfFlagsEnum.HAS_STARTUSER_CHOOSE_MODULES_CC.getCode();
		        bpmnNodeVo.setNodeProperty(NodePropertyEnum.NODE_PROPERTY_CUSTOMIZE.getCode());
	        }
            if(NodeTypeEnum.NODE_TYPE_COPY.getCode().equals(bpmnNodeVo.getNodeType())){
                hasCopy=BpmnConfFlagsEnum.HAS_COPY.getCode();;
            }
            bpmnNodeVo.setIsOutSideProcess(isOutSideProcess);
            bpmnNodeVo.setIsLowCodeFlow(isLowCodeFlow);

            //if the node has no property,the node property default is "1-no property"
            bpmnNodeVo.setNodeProperty(Optional.ofNullable(bpmnNodeVo.getNodeProperty())
                    .orElse(1));

            BpmnNode bpmnNode = new BpmnNode();
            BeanUtils.copyProperties(bpmnNodeVo, bpmnNode);

            if (NodePropertyEnum.NODE_PROPERTY_MEMBER_CONTROL.getCode().equals(oriNodeProperty)) {
                bpmnNode.setExtNodeProperty(oriNodeProperty);
                bpmnNode.setNodeDisplayName(NodePropertyEnum.NODE_PROPERTY_MEMBER_CONTROL.getDesc());
            }

            bpmnNode.setConfId(confId);
            bpmnNode.setCreateTime(new Date());
            bpmnNode.setCreateUser(SecurityUtils.getLogInEmpNameSafe());
            bpmnNodeService.getBaseMapper().insert(bpmnNode);

            Long bpmnNodeId = bpmnNode.getId();
            if(bpmnNodeId==null){
                throw new JiMuBizException("can not get bpmn node id!");
            }

            if(!Objects.isNull(bpmnNodeVo.getNodeProperty())
                    && NodePropertyEnum.NODE_PROPERTY_MEMBER_CONTROL.getCode().equals(oriNodeProperty)) {
                TBpmnMemberControl tBpmnMemberControl = new TBpmnMemberControl();
                tBpmnMemberControl.setFormCode(currentBpmnConf.getFormCode());
                tBpmnMemberControl.setNodeId(bpmnNodeId);
                tBpmnMemberControl.setFieldId(bpmnNodeVo.getNodeRelatedControlId());
                tBpmnMemberControlService.save(tBpmnMemberControl);
            }
            //edit node to
            bpmnNodeToService.editNodeTo(bpmnNodeVo, bpmnNodeId);

            //edit node's button conf
            bpmnNodeButtonConfService.editButtons(bpmnNodeVo, bpmnNodeId);

            //edit node sign up
            bpmnNodeSignUpConfService.editSignUpConf(bpmnNodeVo, bpmnNodeId);


            bpmnNodeVo.setId(bpmnNodeId);
            bpmnNodeVo.setConfId(confId);
            BpmnNodeAdpConfEnum bpmnNodeAdpConfEnum = NodeAdditionalInfoServiceImpl.getBpmnNodeAdpConfEnum(bpmnNodeVo);

            //if it can not get the node's adapter,continue
            if (ObjectUtils.isEmpty(bpmnNodeAdpConfEnum)) {
                continue;
            }

            //edit in node notice template
            bpmnTemplateService.editBpmnTemplate(bpmnNodeVo);


            //edit in node approver remind conf
            bpmnApproveRemindService.editBpmnApproveRemind(bpmnNodeVo);

            //get node adaptor
            BpmnNodeAdaptor bpmnNodeAdaptor = nodeAdditionalInfoService.getBpmnNodeAdaptor(bpmnNodeAdpConfEnum);

            //then edit the node
            bpmnNodeAdaptor.editBpmnNode(bpmnNodeVo);
            if(NodeTypeEnum.NODE_TYPE_COPY.getCode().equals(bpmnNodeVo.getNodeType())&&CollectionUtils.isEmpty(bpmnNodeVo.getNodeTo())){
                hasLastNodeCopy=BpmnConfFlagsEnum.HAS_LAST_NODE_COPY.getCode();
            }



        }
        ProcessorFactory.executePostProcessors(bpmnConfVo);
        Integer extraFlags = bpmnConfVo.getExtraFlags();
        Integer currentFlags=hasStartUserChooseModules|hasCopy|hasLastNodeCopy|hasStartUserChooseModulesCC;
        if(currentFlags!=null&&currentFlags>0){
            Integer binariedOr = BpmnConfFlagsEnum.binaryOr(extraFlags, currentFlags);
            bpmnConfVo.setExtraFlags(binariedOr);
        }
        if (bpmnConfVo.getExtraFlags()!=null) {
            BpmnConf postConf=new BpmnConf();
            postConf.setId(confId);
            postConf.setExtraFlags(bpmnConfVo.getExtraFlags());
            this.updateById(postConf);
        }
	    log.info("发布后自动启动 t_bpmn_conf.id={}", confId);
	    effectiveBpmnConf(confId.intValue());

        // 如果是复制创建，则获取原模版的权限

        BpmnConf oriBpmnConf = this.getBpmnConfByCode(oriBpmnCode);

        if (!Objects.isNull(oriBpmnConf)){
            List<BpmnConfUserPerm> bpmnConfUserPerms = bpmnConfUserPermService
                    .list(new LambdaQueryWrapper<BpmnConfUserPerm>()
                            .eq(BpmnConfUserPerm::getConfId, oriBpmnConf.getId())
                            .eq(BpmnConfUserPerm::getIsDel, 0)
                    );

            if (!CollectionUtils.isEmpty(bpmnConfUserPerms)){
                List<BpmnConfUserPerm> perms = bpmnConfUserPerms.stream()
                        .filter(p -> !(EmpOrgTypeEnum.EMP.getCode().equals(p.getType()) && userId.equals(p.getSourceId())))
                        .peek(p -> {
                            p.setId(null);
                            p.setConfId(confId);
                            p.setCreateTime(null);
                            p.setUpdateTime(null);
                            p.setCreateUser(null);
                            p.setUpdateUser(null);
                        }).collect(Collectors.toList());
                bpmnConfUserPermService.saveBatch(perms);
            }
        }


		// 发布后自动对当前模板设计者可见
		Assert.notNull(user, "用户不存在，请登录正确公司主体下的账号后再发布流程模板!");
		bpmnConfUserPermService.save(
			BpmnConfUserPerm.builder()
				.confId(confId)
				.ehrSource(user.getEhrSource())
				.type(EmpOrgTypeEnum.EMP.getCode())
				.sourceId(userId)
				.sourceName(userName)
				.build());
    }


	/**
	 * 新增模板设计时，自动生成formCode标识
	 * @return 前缀拼接最大id+1
	 */
	private String generateFormCode() {
		String prefix = "MB";
		// get max id
		long number = Optional.ofNullable(bpmnConfMapper.getMaxId()).orElse(1L) + 1;
		// 如果number长不不足6位，前面补0
		String numberStr = String.format("%04d", number);
		String newFormCode = prefix + numberStr;
		// 检查formCode是否已存在
		boolean exists = this.lambdaQuery().eq(BpmnConf::getFormCode, newFormCode).exists();
		if (exists) {
			String existNumStr = String.format("%04d", number + 1);
			return prefix + existNumStr;
		}
		return newFormCode;
	}


    private String getBpmnCode(String bpmnName) {
        BpmnConf.validateBpmnName(bpmnName);
        String bpmnFirstLetters = StrUtils.getFirstLetters(bpmnName);
        String maxBpmnCode = this.getMaxBpmnCode(bpmnFirstLetters);
        if (!Strings.isNullOrEmpty(maxBpmnCode)) {
            return reCheckBpmnCode(bpmnFirstLetters, maxBpmnCode);
        }
        return reCheckBpmnCode(bpmnFirstLetters, bpmnFirstLetters);
    }

    private String getMaxBpmnCode(String bpmnCodeParts) {
        return this.getBaseMapper().getMaxBpmnCode(bpmnCodeParts);
    }

    private String reCheckBpmnCode(String bpmnCodeParts, String bpmnCode) {

        long count = this.getBaseMapper().selectCount(new QueryWrapper<BpmnConf>().eq("bpmn_code", bpmnCode));

        if (count == 0) {
            return bpmnCode;
        }

        String reJoinedBpmnCode = StrUtils.joinBpmnCode(bpmnCodeParts, bpmnCode);

        return reCheckBpmnCode(bpmnCodeParts, reJoinedBpmnCode);

    }

    @Resource
    private TBpmnMemberControlService memberControlService;

    /**
     * query conf detail by id
     *
     * @param id
     * @return
     */
    public BpmnConfVo detail(long id) {
	    String cacheRes = redisTemplate.opsForValue().get(AntFlowConstants.BPMN_CONF_DETAIL_CACHE_PREFIX + id);
	    if (StringUtils.isNotBlank(cacheRes)) {
		    log.info("查询缓存获取模板详情，模板id={}", id);
		    return JSONObject.parseObject(cacheRes, BpmnConfVo.class);
	    } else {
		    log.info("查询数据库获取模板详情，模板id={}", id);
		    BpmnConf bpmnConf = this.getBaseMapper().selectById(id);
		    BpmnConfVo bpmnConfVo = formatConfVo(getBpmnConfVo(bpmnConf));

		    for (BpmnNodeVo node : bpmnConfVo.getNodes()) {
			    if(NodePropertyEnum.NODE_PROPERTY_MEMBER_CONTROL.getCode().equals(node.getExtNodeProperty())){
				    node.setNodeProperty(node.getExtNodeProperty());
				    TBpmnMemberControl tBpmnMemberControl = memberControlService.getOne(new LambdaQueryWrapper<TBpmnMemberControl>().eq(TBpmnMemberControl::getFormCode, bpmnConfVo.getFormCode())
					    .eq(TBpmnMemberControl::getNodeId, node.getId()));
				    if(Objects.nonNull(tBpmnMemberControl)) {
					    node.setNodeRelatedControlId(tBpmnMemberControl.getFieldId());
				    }
			    }
			    node.setLfFieldControlVOs(nodeLfFormdataFieldControlMapper.getFieldControlByNodeId(node.getId()));
		    }
		    String bpmnConfDetailVal = JSONObject.toJSONString(bpmnConfVo);
		    redisTemplate.opsForValue().set(AntFlowConstants.BPMN_CONF_DETAIL_CACHE_PREFIX + id, bpmnConfDetailVal);
		    return bpmnConfVo;
	    }
    }



    /**
     * 逻辑删除模板
     * @param bpmnConfId t_bpmn_conf.id 主标id
     */
    @Transactional
	public void deleteByBpmnConfId(Long bpmnConfId) {
        if(bpmnConfId == null){
            throw new JiMuBizException("invalid bpmnConfId");
        }

        BpmnConf bpmnConf = this.getBaseMapper().selectById(bpmnConfId);
        if(bpmnConf == null){
            throw new JiMuBizException("has not bpmnConf");
        }
        String bpmnCode = bpmnConf.getBpmnCode();
        String formCode = bpmnConf.getFormCode();

        /*
        有审批中的审批单不可删除
         */
        BpmBusinessProcess findProcessCondition = new BpmBusinessProcess();
        findProcessCondition.setProcessinessKey(formCode);
        findProcessCondition.setVersion(bpmnCode);
        findProcessCondition.setProcessState(ProcessStateEnum.HANDLING_STATE.getCode());
		log.info("delete t_bpmn_conf findProcessCondition={}", JSON.toJSONString(findProcessCondition));
        Boolean isExist = bpmBusinessProcessService.isExist(findProcessCondition);
        if (isExist) {
            throw new JiMuBizException("当前模板有未完成的审批单，不可删除");
        }

        /*
        删除
         */
        this.getBaseMapper().updateDelFlagYes(bpmnConfId);
		redisTemplate.delete(AntFlowConstants.BPMN_CONF_DETAIL_CACHE_PREFIX + bpmnConfId);
		log.warn("{}删除了模板：「{}」,模板id={}", SecurityUtils.getLogInEmpNameSafe(), bpmnConf.getBpmnName(), bpmnConfId);
    }


    /**
     * 判断该审批模版是否存在
     * @param bpmnCode
     * @return
     */
    public BpmnConf getBpmnConfByCode(String bpmnCode) {
        if(ObjectUtils.isEmpty(bpmnCode)){
            return null;
        }
        return this.getBaseMapper().selectOne(new QueryWrapper<BpmnConf>()
                .eq("bpmn_code", bpmnCode));
    }

    /**
     * query conf detail by bpmnCode
     * @param bpmnCode
     * @return
     */
    public BpmnConfVo detail(String bpmnCode) {
        BpmnConf bpmnConf = this.getBaseMapper().selectOne(new QueryWrapper<BpmnConf>()
                .eq("bpmn_code", bpmnCode));
        return getBpmnConfVo(bpmnConf);
    }

    /**
     * query conf by formCode
     *
     * @param formCode
     * @return
     */
    public BpmnConfVo detailByFormCode(String formCode) {
        BpmnConf bpmnConf = this.getBaseMapper().selectOne(new QueryWrapper<BpmnConf>()
                .eq("form_code", formCode).eq("effective_status", 1));
        if(bpmnConf==null){
            throw new JiMuBizException("can not get a bpmnConf by provided formCode");
        }
        return getBpmnConfVo(bpmnConf);
    }

    private BpmnConfVo formatConfVo(BpmnConfVo confVo){
        if(confVo==null){
            throw new JiMuBizException("has not confVo");
        }
        List<BpmnNodeVo> nodes = confVo.getNodes();
        if(CollectionUtils.isEmpty(nodes)){
            throw new JiMuBizException("confVo has empty nodes");
        }
        for (BpmnNodeVo node : nodes) {
            BpmnNodePropertysVo property = node.getProperty();
            if(property!=null){
                property.setConditionsConf(null);
            }
        }
        return confVo;
    }
    /**
     * 获得BpmnConfVo
     *
     * @param bpmnConf
     * @return
     */
    private BpmnConfVo getBpmnConfVo(BpmnConf bpmnConf) {
        if (ObjectUtils.isEmpty(bpmnConf)) {
            return new BpmnConfVo();
        }
        BpmnConfVo bpmnConfVo = new BpmnConfVo();
        BeanUtils.copyProperties(bpmnConf, bpmnConfVo);

        String conditionsUrl = "";
        if (bpmnConfVo.getIsOutSideProcess()!=null&&bpmnConf.getIsOutSideProcess()==1) {
            //query and set business party's call url
            OutSideBpmCallbackUrlConf outSideBpmCallbackUrlConf = outSideBpmCallbackUrlConfService
                    .getOutSideBpmCallbackUrlConf(bpmnConf.getId(), bpmnConf.getBusinessPartyId());
            if (outSideBpmCallbackUrlConf!=null) {
                bpmnConfVo.setBpmConfCallbackUrl(outSideBpmCallbackUrlConf.getBpmConfCallbackUrl());//process config call back url
                bpmnConfVo.setBpmFlowCallbackUrl(outSideBpmCallbackUrlConf.getBpmFlowCallbackUrl());//process flow call back url
            }


            //query business party's info
            OutSideBpmBusinessParty outSideBpmBusinessParty = outSideBpmBusinessPartyService.getById(bpmnConf.getBusinessPartyId());

            //set business party's name
            bpmnConfVo.setBusinessPartyName(outSideBpmBusinessParty.getName());

            //set business party's mark,mark just like record is a unique identifier for a certain business party,but for human readability
            bpmnConfVo.setBusinessPartyMark(outSideBpmBusinessParty.getBusinessPartyMark());

            //set business party's business type
            bpmnConfVo.setType(BPMN_FLOW_TYPE_OUTSIDE);

            //query business application url
            BpmProcessAppApplicationVo applicationUrl = applicationService.getApplicationUrl(outSideBpmBusinessParty.getBusinessPartyMark(), bpmnConfVo.getFormCode());


            //set view url,submit url and condition url
            if (applicationUrl!=null) {
                bpmnConfVo.setViewUrl(applicationUrl.getLookUrl());//view url
                bpmnConfVo.setSubmitUrl(applicationUrl.getSubmitUrl());//submit url
                bpmnConfVo.setConditionsUrl(applicationUrl.getConditionUrl());//condition url
                bpmnConfVo.setAppId(applicationUrl.getId());//关联应用Id
                conditionsUrl = applicationUrl.getConditionUrl();
            }
        }
        ProcessorFactory.executePreReadProcessors(bpmnConfVo);
        //set nodes
        List<BpmnNode> bpmnNodes = bpmnNodeService.getBaseMapper().selectList(new QueryWrapper<BpmnNode>()
                .eq("conf_id", bpmnConf.getId())
                .eq("is_del", 0));
        boolean isOutSideProcess=bpmnConf.getIsOutSideProcess()!=null&&bpmnConf.getIsOutSideProcess()==1;
        boolean isLowCodeFlow=bpmnConf.getIsLowCodeFlow()!=null&&bpmnConf.getIsLowCodeFlow()==1;
        if(isOutSideProcess||isLowCodeFlow||bpmnConf.getExtraFlags()!=null){
            for (BpmnNode bpmnNode : bpmnNodes) {
                bpmnNode.setIsOutSideProcess(bpmnConf.getIsOutSideProcess());
                bpmnNode.setIsLowCodeFlow(bpmnConf.getIsLowCodeFlow());
                bpmnNode.setExtraFlags(bpmnConf.getExtraFlags());
            }
        }
        bpmnConfVo.setNodes(getBpmnNodeVoList(bpmnNodes, conditionsUrl));
        if (!ObjectUtils.isEmpty(bpmnConfVo.getNodes())) {
            Map<String,BpmnNodeVo>id2NodeMap=null;
            for (BpmnNodeVo node : bpmnConfVo.getNodes()) {
                    node.setFormCode(bpmnConfVo.getFormCode());
                    if(NodeTypeEnum.NODE_TYPE_PARALLEL_GATEWAY.getCode().equals(node.getNodeType())){
                        BpmnNodeVo aggregationNode = BpmnUtils.getAggregationNode(node, bpmnConfVo.getNodes());
                        if(aggregationNode==null){
                            throw new JiMuBizException("can not find parallel gateway's aggregation node!");
                        }
                        aggregationNode.setAggregationNode(true);
                        aggregationNode.setDeduplicationExclude(true);
                    }
                   /* if(NODE_TYPE_CONDITIONS.getCode().equals(node.getNodeType())&&node.getNodeTo().size()>1){
                        String nodeFrom = node.getNodeFrom();
                        if(id2NodeMap==null){
                            id2NodeMap=bpmnConfVo.getNodes().stream().collect(Collectors.toMap(BpmnNodeVo::getNodeId, o -> o,(k1,k2)->k1));
                        }
                        BpmnNodeVo gatewayNode = id2NodeMap.get(nodeFrom);
                        gatewayNode.setIsParallel(true);
                        BpmnNodeVo aggregationNode = BpmnUtils.getAggregationNode(node, bpmnConfVo.getNodes());
                        if(aggregationNode==null){
                            throw new JiMuBizException("can not find parallel gateway's aggregation node!");
                        }
                        aggregationNode.setAggregationNode(true);
                        aggregationNode.setDeduplicationExclude(true);
                    }*/
            }
        }
        //set viewpage buttons
        setViewPageButton(bpmnConfVo);


        //set out node notice template
        setBpmnTemplateVos(bpmnConfVo);
        return bpmnConfVo;
    }

    /**
     * set out of node notice template
     *
     * @param bpmnConfVo bpmnConfVo
     */
    private void setBpmnTemplateVos(BpmnConfVo bpmnConfVo) {
        bpmnConfVo.setTemplateVos(
                bpmnTemplateService.getBaseMapper().selectList(
                        new QueryWrapper<BpmnTemplate>()
                                .eq("conf_id", bpmnConfVo.getId())
                                .eq("is_del", 0)
                                .isNull("node_id"))
                        .stream()
                        .map(o -> {
                            BpmnTemplateVo vo = new BpmnTemplateVo();
                            BeanUtils.copyProperties(o, vo);
                            buildBpmnTemplateVo(vo);
                            return vo;
                        }).collect(Collectors.toList()));
    }

    private void buildBpmnTemplateVo(BpmnTemplateVo vo) {
        vo.setEventValue(EventTypeEnum.getDescByByCode(vo.getEvent()));
        if (!ObjectUtils.isEmpty(vo.getInforms())) {
            vo.setInformIdList(
                    Arrays.stream(vo.getInforms().split(","))
                            .collect(Collectors.toList()));
            vo.setInformList(vo.getInformIdList()
                    .stream()
                    .map(o -> BaseIdTranStruVo
                            .builder()
                            .id(o)
                            .name(EventTypeEnum.getDescByByCode(Integer.parseInt(o)))
                            .build())
                    .collect(Collectors.toList()));
        }
        if (!ObjectUtils.isEmpty(vo.getEmps())) {
            vo.setEmpIdList(
                    Arrays.stream(vo.getEmps().split(","))
                            .collect(Collectors.toList()));

            Map<String, String> employeeInfo = employeeInfoProvider.provideEmployeeInfo(vo.getEmpIdList());
            vo.setEmpList(vo.getEmpIdList()
                    .stream()
                    .map(o -> BaseIdTranStruVo
                            .builder()
                            .id(o)
                            .name(employeeInfo.get(o))
                            .build())
                    .collect(Collectors.toList()));
        }
        //todo functions to be implemented
        vo.setTemplateName(Optional
                .ofNullable(informationTemplateService.getBaseMapper().selectById(vo.getTemplateId()))
                .orElse(new InformationTemplate())
                .getName());
    }

    /**
     * set view page buttons
     *
     * @param bpmnConfVo
     */
    private void setViewPageButton(BpmnConfVo bpmnConfVo) {
        List<BpmnViewPageButton> bpmnViewPageButtons = bpmnViewPageButtonService.getBaseMapper().selectList(
                new QueryWrapper<BpmnViewPageButton>()
                        .eq("conf_id", bpmnConfVo.getId())
                        .eq("is_del", 0));

        BpmnViewPageButtonBaseVo bpmnViewPageButtonBaseVo = new BpmnViewPageButtonBaseVo();

        //start user's view page
        bpmnViewPageButtonBaseVo.setViewPageStart(getViewPageButtonsByType(bpmnViewPageButtons, ViewPageTypeEnum.VIEW_PAGE_TYPE_START));

        //approver's view page
        bpmnViewPageButtonBaseVo.setViewPageOther(getViewPageButtonsByType(bpmnViewPageButtons, ViewPageTypeEnum.VIEW_PAGE_TYPE_OTHER));

        //set view page buttons
        bpmnConfVo.setViewPageButtons(bpmnViewPageButtonBaseVo);

    }

    /**
     * query view page button list by type
     *
     * @param bpmnViewPageButtons
     * @param viewPageTypeEnum
     * @return
     */
    private List<Integer> getViewPageButtonsByType(List<BpmnViewPageButton> bpmnViewPageButtons, ViewPageTypeEnum viewPageTypeEnum) {
        return bpmnViewPageButtons
                .stream()
                .filter(o -> o.getViewType().intValue() == viewPageTypeEnum.getCode().intValue())
                .collect(Collectors.toList())
                .stream()
                .map(BpmnViewPageButton::getButtonType)
                .collect(Collectors.toList());
    }

    /**
     * convert volist
     *
     * @param bpmnNodeList bpmnNodeList
     * @return List
     */
    private List<BpmnNodeVo> getBpmnNodeVoList(List<BpmnNode> bpmnNodeList, String conditionsUrl) {


        List<Long> idList = bpmnNodeList.stream().map(BpmnNode::getId).collect(Collectors.toList());


        Map<Long, List<String>> bpmnNodeToMap = nodeAdditionalInfoService.getBpmnNodeToMap(idList);


        Map<Long, List<BpmnNodeButtonConf>> bpmnNodeButtonConfMap = getBpmnNodeButtonConfMap(idList);


        Map<Long, BpmnNodeSignUpConf> bpmnNodeSignUpConfMap = getBpmnNodeSignUpConfMap(idList);


        Map<Long, List<BpmnTemplateVo>> bpmnTemplateVoMap = getBpmnTemplateVoMap(idList);


        Map<Long, BpmnApproveRemindVo> bpmnApproveRemindVoMap = getBpmnApproveRemindVoMap(idList);
        Map<Long, List<BpmnNodeLabel>> bpmnNodeLabelsVoMap =new HashMap<>();

        Integer isLowCodeFlow = bpmnNodeList.get(0).getIsLowCodeFlow();
        Integer extraFlags = bpmnNodeList.get(0).getExtraFlags();
        boolean hasNodeLabels = BpmnConfFlagsEnum.hasFlag(extraFlags, BpmnConfFlagsEnum.HAS_NODE_LABELS);
        if(hasNodeLabels){
            bpmnNodeLabelsVoMap=getBpmnNodeLabelsVoMap(idList);
        }
        Map<Long, List<BpmnNodeLfFormdataFieldControl>> bpmnNodeFieldControlConfMap;
        if(isLowCodeFlow!=null&&isLowCodeFlow==1){
            bpmnNodeFieldControlConfMap = getBpmnNodeFieldControlConfMap(idList);
        } else {
            bpmnNodeFieldControlConfMap = null;
        }

        Map<Long, List<BpmnNodeLabel>> finalBpmnNodeLabelsVoMap = bpmnNodeLabelsVoMap;
        List<BpmnNodeVo> bpmnNodeVoList = new ArrayList<>(bpmnNodeList.size());
        for (BpmnNode bpmnNode : bpmnNodeList) {
            BpmnNodeVo bpmnNodeVo = getBpmnNodeVo(bpmnNode, bpmnNodeToMap, bpmnNodeButtonConfMap, bpmnNodeSignUpConfMap,
                    bpmnTemplateVoMap, bpmnApproveRemindVoMap, bpmnNodeFieldControlConfMap, conditionsUrl, finalBpmnNodeLabelsVoMap);
            bpmnNodeVoList.add(bpmnNodeVo);
            //动态条件节点是网关节点,找到网关节点的上一级节点,然后打上标签,流程执行过程中如果有相应标签,则执行动态条件判断
            if(Boolean.TRUE.equals(bpmnNodeVo.getIsDynamicCondition())){
                BpmnNode prevNode= bpmnNodeList.stream().
                        filter(o ->bpmnNodeVo.getNodeFrom().equals(o.getNodeId())).findFirst().orElse(null);

                if(prevNode!=null){
                    List<BpmnNodeLabelVO> labelList = prevNode.getLabelList();
                    if(CollectionUtils.isEmpty(labelList)){
                        labelList = new ArrayList<>();
                        labelList.add(NodeLabelConstants.dynamicCondition);
                        prevNode.setLabelList(labelList);
                    }else{
                        prevNode.getLabelList().add(NodeLabelConstants.dynamicCondition);
                    }
                }else{
                    log.warn("can not find prev node for node:%s");
                }
            }
        }
        return bpmnNodeVoList;

    }

    /**
     * query notice template by ids
     *
     * @param ids ids
     * @return map
     */
    private Map<Long, List<BpmnTemplateVo>> getBpmnTemplateVoMap(List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        return bpmnTemplateService.getBaseMapper().selectList(
                new QueryWrapper<BpmnTemplate>()
                        .in("node_id", ids)
                        .eq("is_del", 0))
                .stream()
                .collect(Collectors.toMap(
                        BpmnTemplate::getNodeId,
                        o -> {
                            BpmnTemplateVo vo = new BpmnTemplateVo();
                            BeanUtils.copyProperties(o, vo);
                            buildBpmnTemplateVo(vo);
                            return new ArrayList<>(Collections.singletonList(vo));
                        },
                        (a, b) -> {
                            a.addAll(b);
                            return a;
                        }));
    }

    private Map<Long, BpmnApproveRemindVo> getBpmnApproveRemindVoMap(List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        return bpmnApproveRemindService.getBaseMapper().selectList(
                new QueryWrapper<BpmnApproveRemind>()
                        .in("node_id", ids)
                        .eq("is_del", 0))
                .stream()
                .collect(Collectors.toMap(
                        BpmnApproveRemind::getNodeId,
                        o -> {
                            BpmnApproveRemindVo vo = new BpmnApproveRemindVo();
                            vo.setIsInuse(false);
                            BeanUtils.copyProperties(o, vo);
                            vo.setTemplateName(Optional
                                    .ofNullable(informationTemplateService.getBaseMapper().selectById(vo.getTemplateId()))
                                    .orElse(new InformationTemplate())
                                    .getName());
                            if (!ObjectUtils.isEmpty(vo.getDays())) {
                                vo.setDayList(Arrays.stream(vo.getDays().split(","))
                                        .map(Integer::parseInt)
                                        .collect(Collectors.toList()));
                            }
                            if (!ObjectUtils.isEmpty(vo.getTemplateId())
                                    && !ObjectUtils.isEmpty(vo.getDays())) {
                                vo.setIsInuse(true);
                            }
                            return vo;
                        },
                        (a, b) -> a));
    }
private Map<Long,List<BpmnNodeLabel>> getBpmnNodeLabelsVoMap(List<Long> ids){
    List<BpmnNodeLabel> nodeLabels = nodeLabelsService.list(Wrappers.<BpmnNodeLabel>lambdaQuery().in(BpmnNodeLabel::getNodeId,ids));
    return nodeLabels.stream().collect(Collectors.groupingBy(BpmnNodeLabel::getNodeId));
}
    /**
     * get node signup conf map
     *
     * @param idList
     * @return
     */
    private Map<Long, BpmnNodeSignUpConf> getBpmnNodeSignUpConfMap(List<Long> idList) {
        return bpmnNodeSignUpConfService.getBaseMapper().selectList(new QueryWrapper<BpmnNodeSignUpConf>()
                .in("bpmn_node_id", idList)
                .eq("is_del", 0)).stream()
                .collect(Collectors.toMap(BpmnNodeSignUpConf::getBpmnNodeId, o -> o));
    }

    /**
     * get button conf map
     *
     * @param idList
     * @return
     */
    private Map<Long, List<BpmnNodeButtonConf>> getBpmnNodeButtonConfMap(List<Long> idList) {
        return bpmnNodeButtonConfService.getBaseMapper().selectList(
                new QueryWrapper<BpmnNodeButtonConf>()
                        .in("bpmn_node_id", idList)
                        .eq("is_del", 0))
                .stream()
                .collect(Collectors.toMap(
                        BpmnNodeButtonConf::getBpmnNodeId,
                        v -> Lists.newArrayList(Collections.singletonList(v)),
                        (a, b) -> {
                            a.addAll(b);
                            return a;
                        }));
    }

    private Map<Long,List<BpmnNodeLfFormdataFieldControl>> getBpmnNodeFieldControlConfMap(List<Long> idList){
        return nodeLfFormdataFieldControlService.list(
                Wrappers.<BpmnNodeLfFormdataFieldControl>lambdaQuery()
                        .in(BpmnNodeLfFormdataFieldControl::getNodeId,idList)
        ).stream()
                .collect(Collectors.toMap(
                        BpmnNodeLfFormdataFieldControl::getNodeId,
                        Lists::newArrayList,
                        (a,b)->{
                            a.addAll(b);
                            return a;
                        }
                ));
    }
    /**
     * convert bpmnnode to nodevo
     *
     * @param bpmnNode bpmnNode
     * @return BpmnNodeVo
     */
    private BpmnNodeVo getBpmnNodeVo(BpmnNode bpmnNode, Map<Long, List<String>> bpmnNodeToMap, Map<Long,
            List<BpmnNodeButtonConf>> bpmnNodeButtonConfMap, Map<Long, BpmnNodeSignUpConf> bpmnNodeSignUpConfMap,
                                     Map<Long, List<BpmnTemplateVo>> bpmnTemplateVoMap,
                                     Map<Long, BpmnApproveRemindVo> bpmnApproveRemindVoMap,
                                     Map<Long, List<BpmnNodeLfFormdataFieldControl>> lfFieldControlMap,
                                     String conditionsUrl, Map<Long, List<BpmnNodeLabel>> bpmnNodeLabelsVoMap) {


        BpmnNodeVo bpmnNodeVo = new BpmnNodeVo();
        BeanUtils.copyProperties(bpmnNode, bpmnNodeVo);


        //set nodeto
        bpmnNodeVo.setNodeTo(bpmnNodeToMap.get(bpmnNode.getId()));

        //set buttons conf
        setButtons(bpmnNodeVo, bpmnNodeButtonConfMap.get(bpmnNode.getId()));

        //assign property name
        bpmnNodeVo.setNodePropertyName(NodePropertyEnum.getDescByCode(bpmnNodeVo.getNodeProperty()));


        //set in node notice template
        bpmnNodeVo.setTemplateVos(bpmnTemplateVoMap.get(bpmnNode.getId()));


        //set in node approvement remind
        bpmnNodeVo.setApproveRemindVo(bpmnApproveRemindVoMap.get(bpmnNode.getId()));


        BpmnNodeAdpConfEnum bpmnNodeAdpConfEnum = NodeAdditionalInfoServiceImpl.getBpmnNodeAdpConfEnum(bpmnNodeVo);


        if (ObjectUtils.isEmpty(bpmnNodeAdpConfEnum)) {
            return bpmnNodeVo;
        }

        //get node adaptor
        BpmnNodeAdaptor bpmnNodeAdaptor = getBpmnNodeAdaptor(bpmnNodeAdpConfEnum);

        //use adaptor to format nodevo
	    log.warn("----> {} --- bpmnNodeAdaptor= {}", bpmnNodeVo.getNodeName(), bpmnNodeAdaptor);
        bpmnNodeAdaptor.formatToBpmnNodeVo(bpmnNodeVo);


        if (NodeTypeEnum.NODE_TYPE_OUT_SIDE_CONDITIONS.getCode().equals(bpmnNode.getNodeType())) {
            bpmnNodeVo.setNodeType(NodeTypeEnum.NODE_TYPE_CONDITIONS.getCode());
        }

        //set sign up conf
        setBpmnNodeSignUpConf(bpmnNode, bpmnNodeSignUpConfMap, bpmnNodeVo);
        setFieldControlVOs(bpmnNode,lfFieldControlMap,bpmnNodeVo);
        List<BpmnNodeLabel> nodeLabels = bpmnNodeLabelsVoMap.get(bpmnNode.getId());
        if(!CollectionUtils.isEmpty(nodeLabels)){
            List<BpmnNodeLabelVO> labelVOList = nodeLabels.stream().map(a -> new BpmnNodeLabelVO(a.getLabelName(), a.getLabelValue())).collect(Collectors.toList());
            bpmnNodeVo.setLabelList(labelVOList);
        }

        return bpmnNodeVo;
    }

    /**
     * get node adaptor
     *
     * @param bpmnNodeAdpConfEnum
     * @return
     */
    private BpmnNodeAdaptor getBpmnNodeAdaptor(BpmnNodeAdpConfEnum bpmnNodeAdpConfEnum) {

        return adaptorFactory.getBpmnNodeAdaptor(bpmnNodeAdpConfEnum);
    }


    /**
     * set buttons
     *
     * @param bpmnNodeVo
     */
    private void setButtons(BpmnNodeVo bpmnNodeVo, List<BpmnNodeButtonConf> bpmnNodeButtonConfs) {

        if (!ObjectUtils.isEmpty(bpmnNodeButtonConfs)) {

            BpmnNodeButtonConfBaseVo buttons = new BpmnNodeButtonConfBaseVo();


            buttons.setStartPage(getButtons(bpmnNodeButtonConfs, ButtonPageTypeEnum.INITIATE));


            buttons.setApprovalPage(getButtons(bpmnNodeButtonConfs, ButtonPageTypeEnum.AUDIT));


            bpmnNodeVo.setButtons(buttons);

        }

    }

    /**
     * get buttons list
     *
     * @param bpmnNodeButtonConfs
     * @param buttonPageTypeEnum
     * @return
     */
    private List<Integer> getButtons(List<BpmnNodeButtonConf> bpmnNodeButtonConfs, ButtonPageTypeEnum buttonPageTypeEnum) {
        return bpmnNodeButtonConfs
                .stream()
                .filter(o -> o.getButtonPageType().intValue() == buttonPageTypeEnum.getCode())
                .map(BpmnNodeButtonConf::getButtonType)
                .collect(Collectors.toList())
                .stream()
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * set node sign up conf
     *
     * @param bpmnNode
     * @param bpmnNodeSignUpConfMap
     * @param bpmnNodeVo
     */
    private void setBpmnNodeSignUpConf(BpmnNode bpmnNode, Map<Long, BpmnNodeSignUpConf> bpmnNodeSignUpConfMap, BpmnNodeVo bpmnNodeVo) {
        if (bpmnNode.getIsSignUp() != 1) {
            return;
        }
        BpmnNodeSignUpConf bpmnNodeSignUpConf = bpmnNodeSignUpConfMap.get(bpmnNode.getId());
        if (ObjectUtils.isEmpty(bpmnNodeSignUpConf)) {
            return;
        }
        BpmnNodePropertysVo propertysVo = bpmnNodeVo.getProperty();
        if (ObjectUtils.isEmpty(propertysVo)) {
            propertysVo = new BpmnNodePropertysVo();
        }
        propertysVo.setAfterSignUpWay(bpmnNodeSignUpConf.getAfterSignUpWay());
        propertysVo.setSignUpType(bpmnNodeSignUpConf.getSignUpType());
        bpmnNodeVo.setProperty(propertysVo);
    }


    private void setFieldControlVOs(BpmnNode bpmnNode,Map<Long,List<BpmnNodeLfFormdataFieldControl>> fieldControlMap,BpmnNodeVo nodeVo){
        boolean isLowFlow=Objects.equals(bpmnNode.getIsLowCodeFlow(),1);
        if(!isLowFlow){
            return;
        }
        if(CollectionUtils.isEmpty(fieldControlMap)){

            return;
        }
        List<BpmnNodeLfFormdataFieldControl> fieldControls = fieldControlMap.get(bpmnNode.getId());
        if(CollectionUtils.isEmpty(fieldControls)){
            return;
        }
        List<LFFieldControlVO> fieldControlVOS=new ArrayList<>();
        for (BpmnNodeLfFormdataFieldControl fieldControl : fieldControls) {
            LFFieldControlVO lfFieldControlVO=new LFFieldControlVO();
            lfFieldControlVO.setFieldId(fieldControl.getFieldId());
            lfFieldControlVO.setFieldName(fieldControl.getFieldName());
            lfFieldControlVO.setPerm(fieldControl.getPerm());
            fieldControlVOS.add(lfFieldControlVO);
        }
        nodeVo.setLfFieldControlVOs(fieldControlVOS);
    }
    /**
     * effective bpmn conf
     *
     * @param id
     */
    public void effectiveBpmnConf(Integer id) {
        BpmnConf bpmnConf = this.getBaseMapper().selectById(id);
        AssertUtil.throwsIfEmpty(bpmnConf,"未能根据id:查询到指定配置!",Lists.newArrayList(id));

        //query the old effective workflow configuration by formcode
        BpmnConf confInDb = this.getBaseMapper().selectOne(new QueryWrapper<BpmnConf>()
                .eq("form_code", bpmnConf.getFormCode())
                .eq("effective_status", 1));

        if (!ObjectUtils.isEmpty(confInDb)) {
            //set the old one effective status to zero
            confInDb.setEffectiveStatus(0);
            this.updateById(confInDb);
        }else{
            confInDb=new BpmnConf();
        }
        this.updateById(BpmnConf
                .builder()
                .id(id.longValue())
                .appId(confInDb.getAppId())
                .bpmnType(confInDb.getBpmnType())
                .isAll(getIsAll(bpmnConf, confInDb))
                .effectiveStatus(1)
                .build());

        bpmProcessNameService.editProcessName(bpmnConf);
    }

    private Integer getIsAll(BpmnConf bpmnConf, BpmnConf beforeBpmnConf) {
        if (bpmnConf.getIsOutSideProcess() == 1) {
            return 1;
        } else {
            if (!ObjectUtils.isEmpty(beforeBpmnConf.getIsAll())) {
                return beforeBpmnConf.getIsAll();
            }
        }
        return 0;
    }
    /**
     * get conf list with paging
     *
     * @param pageDto
     * @param vo
     * @return
     */
    public ResultAndPage<BpmnConfVo> selectPage(PageDto pageDto, BpmnConfVo vo) {
        //use mybatus plus's paging plugin,mbatis plus is very popular in China even all over the world
        Page<BpmnConfVo> page = PageUtils.getPageByPageDto(pageDto);
        List<BpmnConfVo> bpmnConfVos = this.getBaseMapper().selectPageList(page, vo);
        if (bpmnConfVos==null) {
            return PageUtils.getResultAndPage(page);
        }
		if (Objects.isNull(vo.getIsOutSideProcess())) { // 默认0，防止NPE空指针
            vo.setIsOutSideProcess(0);
        }
	    // 项目改动为：模板发布时自动启动，没有手动点启动这个概念了，所以对应的启动状态effectiveStatus设置条件为1
		if (vo.getEffectiveStatus() == null && vo.getIsOutSideProcess() == 0) {
            vo.setEffectiveStatus(1);
        }
        if (vo.getIsOutSideProcess() == 1){
            List<BpmProcessAppApplication> bizAppList = bpmProcessAppApplicationService.selectApplicationList();
            Map<String, String>  bizAppMap= bizAppList
                    .stream()
                    .collect(Collectors.toMap(p->p.getProcessKey(),p->p.getTitle()));
            for (BpmnConfVo record : bpmnConfVos) {
                if (record.getIsOutSideProcess() == 1){
                    record.setFormCodeDisplayName(bizAppMap.get(record.getFormCode()));
                }
            }
        }
        if (vo.getIsOutSideProcess() == 0){
            List<DIYProcessInfoDTO> diyFormCodeList = TaskMgmtService.viewProcessInfo(null);
            Map<String, String>  diyFormCodes= diyFormCodeList
                    .stream()
                    .collect(Collectors.toMap(p->p.getKey(),p->p.getValue()));
            for (BpmnConfVo record : bpmnConfVos) {
                if (record.getIsLowCodeFlow() == 0 && record.getIsOutSideProcess() == 0){
                    record.setFormCodeDisplayName(diyFormCodes.get(record.getFormCode()));
                }
            }
        }
        page.setRecords(bpmnConfVos
                .stream()
                .peek(o -> o.setDeduplicationTypeName(DeduplicationTypeEnum.getDescByCode(o.getDeduplicationType())))
                .collect(Collectors.toList()));
        return PageUtils.getResultAndPage(page);
    }


    /**
     * format outside process form code
     *
     * @param bpmnConfVo
     * @return
     */
    private String formatOutSideFormCode(BpmnConfVo bpmnConfVo) {
        String formCode = bpmnConfVo.getFormCode();
        return formCode.substring(formCode.indexOf(linkMark) + 1);
    }

	/**
	 * 发起人的表单权限信息
	 */
	public Result<List<LFFieldControlVO>> getFieldControlListOfSponsor(Long confId) {
		BpmnConf bpmnConf = this.lambdaQuery().eq(BpmnConf::getId, confId).last("limit 1").one();
		if (ObjectUtils.isEmpty(bpmnConf)) {
			log.error("当前登录用户 发起人的表单权限获取失败,发起人node节点id获取失败");
			return Result.newSuccessResult(Collections.emptyList());
		}
		Long nodeId = Optional.ofNullable(bpmnNodeService.lambdaQuery()
			.select(BpmnNode::getId)
			.eq(BpmnNode::getConfId, confId)
			.eq(BpmnNode::getNodeType, NodeTypeEnum.NODE_TYPE_START.getCode())
			.last("limit 1").one()).map(BpmnNode::getId).orElse(null);
		if (Objects.isNull(nodeId)) {
			log.error("发起人的表单权限获取失败,发起人node节点id获取失败");
			return Result.newSuccessResult(Collections.emptyList());
		}
		return Result.newSuccessResult(nodeLfFormdataFieldControlMapper.getFieldControlByNodeId(nodeId));
	}

	/**
	 * 更新模板数据权限
	 */
	@Transactional
	public Result<Boolean> updateBpmnConfPerm(AddBpmnConfPermReqDto req) {
		if (CollectionUtils.isEmpty(req.getBpmnConfUserPermVo())) {
			return Result.newFailureResult("修改模板数据权限失败,请检查参数是否正确");
		}
		String loginEmpName = SecurityUtils.getLogInEmpName();
		log.info("{}正在编辑模板的可见范围权限，模板id={}, 可见范围={}", loginEmpName, req.getConfId(), JSON.toJSONString(req.getBpmnConfUserPermVo()));
		if (bpmnConfUserPermService.lambdaQuery().eq(BpmnConfUserPerm::getConfId, req.getConfId()).exists()) {
			bpmnConfUserPermService.lambdaUpdate()
				.eq(BpmnConfUserPerm::getConfId, req.getConfId())
				.set(BpmnConfUserPerm::getIsDel, YesOrNoEnum.YES.getCode())
				.update();
		}
		Set<BpmnConfUserPerm> entities = new HashSet<>();
		for (AddBpmnConfPermReqDto.BpmnConfUserPermReqDto permVo : req.getBpmnConfUserPermVo()) {
			BpmnConfUserPerm entity = new BpmnConfUserPerm();
			entity.setConfId(req.getConfId());
			entity.setEhrSource(permVo.getEhrSource());
			entity.setType(permVo.getControlDataType());
			entity.setSourceId(permVo.getId());
			entity.setSourceName(permVo.getName());
			entity.setCreateUser(loginEmpName);
			entity.setUpdateUser(loginEmpName);
			entities.add(entity);
		}
		return Result.newSuccessResult(bpmnConfUserPermService.saveBatch(entities));
	}



	/**
	 * 查询当前模板的数据权限
	 */
	public Result<List<BpmnConfUserPermResVo>> findBpmnConfPerm(Long confId) {
		List<BpmnConfUserPerm> bpmnConfUserPerms = bpmnConfUserPermService.lambdaQuery().eq(BpmnConfUserPerm::getConfId, confId)
			.eq(BpmnConfUserPerm::getIsDel, YesOrNoEnum.NO.getCode()).list();
		if (CollectionUtils.isEmpty(bpmnConfUserPerms)) {
			return Result.newSuccessResult(Collections.emptyList());
		}
		List<BpmnConfUserPermResVo> bpmnConfUserPermResVos = bpmnConfUserPerms.stream().map(n -> {
			BpmnConfUserPermResVo bpmnConfUserPermResVo = new BpmnConfUserPermResVo();
			BeanUtils.copyProperties(n, bpmnConfUserPermResVo);
			return bpmnConfUserPermResVo;
		}).collect(Collectors.toList());
		return Result.newSuccessResult(bpmnConfUserPermResVos);
	}

	/**
	 * 修改用户标签权限
	 * @param userTagPermReqDto 用户标签权限信息
	 * @return 是否新增/修改成功
	 */
	public Boolean modifyUserTagPerm(AddUserTagPermReqDto userTagPermReqDto) {
		if (CollectionUtils.isEmpty(userTagPermReqDto.getUserTagPermReqDtoList())) {
			throw new JiMuBizException("更新用户标签权限失败，用户标签权限信息不能为空");
		}
		String loginEmpName = SecurityUtils.getLogInEmpName();
		if (userTagPermService.lambdaQuery().eq(UserTagPerm::getRoleId, userTagPermReqDto.getRoleId()).exists()) {
			userTagPermService.lambdaUpdate()
				.eq(UserTagPerm::getRoleId, userTagPermReqDto.getRoleId())
				.set(UserTagPerm::getIsDel, YesOrNoEnum.YES.getCode())
				.update();
		}
		Set<UserTagPerm> entities = new HashSet<>();
		for (AddUserTagPermReqDto.UserTagPermReqDto permVo : userTagPermReqDto.getUserTagPermReqDtoList()) {
			UserTagPerm entity = new UserTagPerm();
			entity.setRoleId(userTagPermReqDto.getRoleId());
			entity.setEhrSource(permVo.getEhrSource());
			entity.setType(permVo.getControlDataType());
			entity.setSourceId(permVo.getId());
			entity.setSourceName(permVo.getName());
			entity.setCreateUser(loginEmpName);
			entity.setUpdateUser(loginEmpName);
			entities.add(entity);
		}
		return userTagPermService.saveBatch(entities);
	}


	/**
	 * 查询用户标签权限
	 * @param roleId 用户标签id
	 * @return 用户标签权限列表
	 */
	public List<BpmnConfUserPermResVo> findUserTagPerm(Long roleId) {
		List<UserTagPerm> userTagPerms = userTagPermService.lambdaQuery().eq(UserTagPerm::getRoleId, roleId).eq(UserTagPerm::getIsDel, YesOrNoEnum.NO.getCode()).list();
		if (CollectionUtils.isEmpty(userTagPerms)) {
			return Collections.emptyList();
		}
		return BeanCopyUtils.convertToVoList(userTagPerms, BpmnConfUserPermResVo.class);
	}
}
