package org.openoa.engine.bpmnconf.service;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.openoa.base.constant.enums.ButtonTypeEnum;
import org.openoa.base.constant.enums.NodeTypeEnum;
import org.openoa.base.constant.enums.ProcessNodeEnum;
import org.openoa.base.interf.AdaptorService;
import org.openoa.base.vo.*;
import org.openoa.common.constant.enus.ElementPropertyEnum;
import org.openoa.common.util.BpmnElementUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.openoa.base.constant.enums.ButtonTypeEnum.BUTTON_TYPE_AGREE;
import static org.openoa.base.constant.enums.ButtonTypeEnum.BUTTON_TYPE_DISAGREE;

@Slf4j
public abstract class MyBpmnElementAdaptor implements AdaptorService {

    /**
     * get the BpmnConfCommonElementVo object
     *
     * @return
     */
    protected abstract BpmnConfCommonElementVo getElementVo(BpmnNodePropertysVo property, BpmnNodeParamsVo params,
                                                            Integer elementCode, String elementId);

    /**
     * to format BpmnNodeVo to BpmnConfCommonElementVo list
     * @param bpmnConfCommonElementVos
     * @param nodeVo
     * @param nodeCode
     * @param sequenceFlowNum
     */
    public void doFormatNodesToElements(List<BpmnConfCommonElementVo> bpmnConfCommonElementVos, BpmnNodeVo nodeVo, Integer nodeCode, Integer sequenceFlowNum, HashMap<String, Integer> numMap) {

		if (Objects.nonNull(nodeVo) && Objects.equals(nodeVo.getNodeType(), NodeTypeEnum.NODE_TYPE_COPY.getCode())) {
			return;
		}
        BpmnConfCommonElementVo elementVo = formatNodesToElements(bpmnConfCommonElementVos, nodeVo, nodeCode, sequenceFlowNum, numMap);
        elementVo.setNodeId(nodeVo.getId().toString());
        elementVo.setLabelList(nodeVo.getLabelList());
        //dealing with sign up element
        doSignUp(bpmnConfCommonElementVos, elementVo, numMap);
    }


    private BpmnConfCommonElementVo formatNodesToElements(List<BpmnConfCommonElementVo> bpmnConfCommonElementVos, BpmnNodeVo nodeVo, Integer nodeCode, Integer sequenceFlowNum, HashMap<String, Integer> numMap) {

        String elementId = ProcessNodeEnum.getDescByCode(nodeCode+1);


        nodeVo.setElementId(elementId);
        BpmnConfCommonElementVo elementVo = getElementVo(nodeVo.getProperty(), nodeVo.getParams(),
                nodeCode+1, elementId);

		if (Objects.isNull(nodeVo.getButtons()) || Objects.isNull(nodeVo.getButtons().getStartPage()) || Objects.isNull(nodeVo.getButtons().getApprovalPage())) {
			log.info("formatNodesToElements call1,  nodeVo={}, elementVo={}", JSON.toJSONString(nodeVo), JSON.toJSONString(elementVo));
		}
        setElementButtons(nodeVo, elementVo);


        elementVo.setTemplateVos(nodeVo.getTemplateVos());


        elementVo.setApproveRemindVo(nodeVo.getApproveRemindVo());


        setSignUpProperty(nodeVo, elementVo);


        bpmnConfCommonElementVos.add(elementVo);

        boolean hasAlreadyFlowTo=false;

        if(!CollectionUtils.isEmpty(nodeVo.getFromNodes())){
            hasAlreadyFlowTo=true;
            BpmnConfCommonElementVo parallelGateWayElement = BpmnElementUtils.getParallelGateWayElement(sequenceFlowNum+1);
            bpmnConfCommonElementVos.add(parallelGateWayElement);
            String parallelGateWayElementElementId = parallelGateWayElement.getElementId();
            sequenceFlowNum++;
            List<BpmnNodeVo> fromNodes = nodeVo.getFromNodes();

            for (BpmnNodeVo fromNode : fromNodes) {
                String fromNodeElementId = fromNode.getElementId();
                bpmnConfCommonElementVos.add(BpmnElementUtils.getSequenceFlow(sequenceFlowNum+1,
                        fromNodeElementId , parallelGateWayElementElementId));
                sequenceFlowNum++;
            }
            bpmnConfCommonElementVos.add(BpmnElementUtils.getSequenceFlow(sequenceFlowNum+1,
                    parallelGateWayElementElementId , elementVo.getElementId()));

        }
        if(!hasAlreadyFlowTo){
            for (BpmnConfCommonElementVo bpmnConfCommonElementVo : bpmnConfCommonElementVos) {
                if(elementVo.getElementId().equals(bpmnConfCommonElementVo.getFlowTo())){
                    hasAlreadyFlowTo=true;
                    break;
                }
            }
        }
        if(!hasAlreadyFlowTo){
            bpmnConfCommonElementVos.add(BpmnElementUtils.getSequenceFlow(sequenceFlowNum+1,
                    ProcessNodeEnum.getDescByCode(nodeCode), elementVo.getElementId()));
        }

        nodeCode++;
        sequenceFlowNum++;

        numMap.put("nodeCode", nodeCode);
        numMap.put("sequenceFlowNum", sequenceFlowNum);

        return elementVo;
    }

    /**
     * set sign up node property
     *
     * @param nodeVo
     * @param elementVo
     */
    private void setSignUpProperty(BpmnNodeVo nodeVo, BpmnConfCommonElementVo elementVo) {
        elementVo.setIsSignUp(Optional.ofNullable(nodeVo.getIsSignUp()).orElse(0));
        elementVo.setAfterSignUpWay(Optional.ofNullable(nodeVo.getProperty()).orElse(new BpmnNodePropertysVo()).getAfterSignUpWay());
        elementVo.setSignUpType(Optional.ofNullable(nodeVo.getProperty()).orElse(new BpmnNodePropertysVo()).getSignUpType());
    }

    /**
     * do sign up
     *
     * @param bpmnConfCommonElementVos
     * @param numMap
     * @param fatherElementVo
     */
    private void doSignUp(List<BpmnConfCommonElementVo> bpmnConfCommonElementVos, BpmnConfCommonElementVo fatherElementVo, HashMap<String, Integer> numMap) {
        if (Objects.equals(fatherElementVo.getIsSignUp(), 1)) {
            switch (fatherElementVo.getAfterSignUpWay()) {
                case 1://go back to assigner
                    backApproval(bpmnConfCommonElementVos, fatherElementVo, numMap);
                    break;
                case 2://
                default://do not go back to assigner
                    unbackApproval(bpmnConfCommonElementVos, fatherElementVo, numMap);
            }
        }
    }


    private void backApproval(List<BpmnConfCommonElementVo> bpmnConfCommonElementVos, BpmnConfCommonElementVo fatherElementVo, HashMap<String, Integer> numMap) {


        BpmnConfCommonElementVo signUpSubElementVo = setAndGetSignUpSubElement(bpmnConfCommonElementVos, fatherElementVo, numMap.get("nodeCode"), numMap.get("sequenceFlowNum"), numMap);


        addBackApproval(bpmnConfCommonElementVos, fatherElementVo, signUpSubElementVo, numMap.get("nodeCode"), numMap.get("sequenceFlowNum"), numMap);

    }


    private void addBackApproval(List<BpmnConfCommonElementVo> bpmnConfCommonElementVos, BpmnConfCommonElementVo fatherElementVo, BpmnConfCommonElementVo signUpSubElementVo, Integer nodeCode, Integer sequenceFlowNum, HashMap<String, Integer> numMap) {
        Integer elementCode = nodeCode + 1;
        String elementId = ProcessNodeEnum.getDescByCode(elementCode);
        Integer elementSequenceFlowNum = sequenceFlowNum + 1;

        BpmnConfCommonElementVo backApprovalElementVo = BpmnElementUtils.getSignUpElement(elementId, signUpSubElementVo, ElementPropertyEnum.ELEMENT_PROPERTY_SIGN_UP_SERIAL.getCode());
        backApprovalElementVo.setElementName(fatherElementVo.getElementName());//set element name(same as add sign up element)
        backApprovalElementVo.setIsSignUpSubElement(1);//set sign up sub element
        backApprovalElementVo.setIsBackSignUp(1); //set back sign up
        backApprovalElementVo.setSignUpElementId(fatherElementVo.getElementId());//set sign up element id


        setSignUpElementButtions(backApprovalElementVo);


        bpmnConfCommonElementVos.add(backApprovalElementVo);


        bpmnConfCommonElementVos.add(BpmnElementUtils.getSequenceFlow(elementSequenceFlowNum,
                ProcessNodeEnum.getDescByCode(nodeCode), backApprovalElementVo.getElementId()));

        nodeCode++;
        sequenceFlowNum++;

        numMap.put("nodeCode", nodeCode);
        numMap.put("sequenceFlowNum", sequenceFlowNum);
    }

    /**
     * do not go back to assigner
     *
     * @param bpmnConfCommonElementVos
     * @param fatherElementVo
     * @param numMap
     */
    private void unbackApproval(List<BpmnConfCommonElementVo> bpmnConfCommonElementVos, BpmnConfCommonElementVo fatherElementVo, HashMap<String, Integer> numMap) {


        //set and get the sign up element
        setAndGetSignUpSubElement(bpmnConfCommonElementVos, fatherElementVo, numMap.get("nodeCode"), numMap.get("sequenceFlowNum"), numMap);

    }

    /**
     * set and get the sign up element
     *
     * @param bpmnConfCommonElementVos
     * @param fatherElementVo
     * @param nodeCode
     * @param sequenceFlowNum
     * @return
     */
    private BpmnConfCommonElementVo setAndGetSignUpSubElement(List<BpmnConfCommonElementVo> bpmnConfCommonElementVos, BpmnConfCommonElementVo fatherElementVo, Integer nodeCode, Integer sequenceFlowNum, HashMap<String, Integer> numMap) {
        Integer elementCode = nodeCode + 1;
        String elementId = ProcessNodeEnum.getDescByCode(elementCode);
        Integer elementSequenceFlowNum = sequenceFlowNum + 1;


        //build the sign up element by type
        BpmnConfCommonElementVo signUpElementVo;
        switch (fatherElementVo.getSignUpType()) {
            case 1://sign up in sequence
                signUpElementVo = BpmnElementUtils.getSignUpElement(elementId, fatherElementVo, ElementPropertyEnum.ELEMENT_PROPERTY_SIGN_UP_SERIAL.getCode());
                break;
            case 2://all sign(with no sequence)
                signUpElementVo = BpmnElementUtils.getSignUpElement(elementId, fatherElementVo, ElementPropertyEnum.ELEMENT_PROPERTY_SIGN_UP_PARALLEL.getCode());
                break;
            case 3://or sign
                signUpElementVo = BpmnElementUtils.getSignUpElement(elementId, fatherElementVo, ElementPropertyEnum.ELEMENT_PROPERTY_SIGN_UP_PARALLEL_OR.getCode());
                break;
            default://default to sequential all sign
                signUpElementVo = BpmnElementUtils.getSignUpElement(elementId, fatherElementVo, ElementPropertyEnum.ELEMENT_PROPERTY_SIGN_UP_SERIAL.getCode());
        }

        signUpElementVo.setIsSignUpSubElement(1);//set is sign up flag
        signUpElementVo.setSignUpElementId(fatherElementVo.getElementId());//set sign up element id


        setSignUpElementButtions(signUpElementVo);


        bpmnConfCommonElementVos.add(signUpElementVo);

        //set sequence flow
        BpmnConfCommonElementVo signUpSequenceFlow = BpmnElementUtils.getSequenceFlow(elementSequenceFlowNum,
                ProcessNodeEnum.getDescByCode(nodeCode), signUpElementVo.getElementId());
        signUpSequenceFlow.setIsSignUpSequenceFlow(1);
        bpmnConfCommonElementVos.add(signUpSequenceFlow);

        nodeCode++;
        sequenceFlowNum++;

        numMap.put("nodeCode", nodeCode);
        numMap.put("sequenceFlowNum", sequenceFlowNum);

        return signUpElementVo;
    }

    /**
     * set element button
     *
     * @param nodeVo
     * @param elementVo
     */
    protected void setElementButtons(BpmnNodeVo nodeVo, BpmnConfCommonElementVo elementVo) {
        elementVo.setButtons(BpmnConfCommonButtonsVo
                .builder()
                .startPage(nodeVo.getButtons().getStartPage()
                        .stream()
                        .map(o -> BpmnConfCommonButtonPropertyVo
                                .builder()
                                .buttonType(o)
                                .buttonName(ButtonTypeEnum.getDescByCode(o))
                                .build())
                        .collect(Collectors.toList()))
                .approvalPage(nodeVo.getButtons().getApprovalPage()
                        .stream()
                        .map(o -> BpmnConfCommonButtonPropertyVo
                                .builder()
                                .buttonType(o)
                                .buttonName(ButtonTypeEnum.getDescByCode(o))
                                .build())
                        .collect(Collectors.toList()))
                .build());
    }

    /**
     * set sign up element button
     *
     * @param elementVo
     */
    private void setSignUpElementButtions(BpmnConfCommonElementVo elementVo) {
        elementVo.setButtons(BpmnConfCommonButtonsVo
                .builder()
                .approvalPage(Lists.newArrayList(
                        BpmnConfCommonButtonPropertyVo
                                .builder()
                                .buttonType(BUTTON_TYPE_AGREE.getCode())
                                .buttonName(BUTTON_TYPE_AGREE.getDesc())
                                .build(),
                        BpmnConfCommonButtonPropertyVo
                                .builder()
                                .buttonType(BUTTON_TYPE_DISAGREE.getCode())
                                .buttonName(BUTTON_TYPE_DISAGREE.getDesc())
                                .build()
                ))
                .build());
    }

}