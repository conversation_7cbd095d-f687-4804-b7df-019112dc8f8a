package org.openoa.engine.conf.aspect;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskInfo;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.openoa.base.constant.StringConstants;
import org.openoa.base.constant.enums.NodeNoHeaderActionEnum;
import org.openoa.base.constant.enums.ProcessOperationEnum;
import org.openoa.base.dto.wecom.NodeHeadActionDto;
import org.openoa.base.entity.BpmBusinessProcess;
import org.openoa.base.entity.Employee;
import org.openoa.base.entity.User;
import org.openoa.base.exception.JiMuBizException;
import org.openoa.base.mapper.UserMapper;
import org.openoa.base.service.wecom.NoticeMsgEnum;
import org.openoa.base.util.SecurityUtils;
import org.openoa.base.util.ThreadLocalContainer;
import org.openoa.base.vo.BaseIdTranStruVo;
import org.openoa.base.vo.BusinessDataVo;
import org.openoa.base.vo.TaskMgmtVO;
import org.openoa.engine.bpmnconf.confentity.BpmProcessForward;
import org.openoa.engine.bpmnconf.confentity.BpmnConf;
import org.openoa.engine.bpmnconf.confentity.BpmnNode;
import org.openoa.engine.bpmnconf.mapper.BpmBusinessProcessMapper;
import org.openoa.engine.bpmnconf.mapper.TaskMgmtMapper;
import org.openoa.engine.bpmnconf.service.biz.BpmBusinessProcessServiceImpl;
import org.openoa.engine.bpmnconf.service.biz.ButtonOperationServiceImpl;
import org.openoa.engine.bpmnconf.service.biz.NotifyServiceImpl;
import org.openoa.engine.bpmnconf.service.biz.ResubmitProcessImpl;
import org.openoa.engine.bpmnconf.service.impl.*;
import org.openoa.engine.factory.FormFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

import static org.openoa.base.constant.enums.ProcessOperationEnum.*;

/**
 * <AUTHOR>
 * @Date 2024/7/7 9:19
 * @Version 1.0
 */
@Slf4j
@Aspect
@Component
public class DoButtonOperationAspect {
    @Autowired
    private FormFactory formFactory;
    @Autowired
    private ButtonOperationServiceImpl buttonOperationService;
	@Resource
	private BpmnConfServiceImpl bmpnConfService;
	@Resource
	private UserMapper userMapper;

    @Resource
    private NotifyServiceImpl notifyService;
	@Resource
	protected BpmBusinessProcessServiceImpl processService;
	@Resource
	protected BpmBusinessProcessServiceImpl bpmBusinessProcessService;
	@Resource
	private TaskService taskService;
	@Resource
	private TaskMgmtMapper taskMgmtMapper;
	@Resource
	private BpmFlowrunEntrustServiceImpl bpmFlowrunEntrustService;
	@Resource
	private ResubmitProcessImpl resubmitProcessImpl;
	@Resource
	private BpmnNodeServiceImpl bpmnNodeService;
	@Resource
	private BpmnNodeToServiceImpl bpmnNodeToService;
	@Resource
	private BpmProcessForwardServiceImpl bpmProcessForwardService;

	@Resource
	private BpmBusinessProcessMapper bpmBusinessProcessMapper;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	// 文案
	private static final String NEXT_NODE_DISPLAY_NAME = "审核人为空，自动通过";

    @Around("execution(* org.openoa.engine.factory.ButtonPreOperationService.buttonsPreOperation(..))")
    public Object around(ProceedingJoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args.length < 3) {
            throw new IllegalArgumentException("method must hava three parameters");
        }
        String params = (String) args[0];
        String formCode = (String) args[1];
	    HttpServletRequest request = (HttpServletRequest) args[2];
        log.info("params:{},formCode:{}", params, formCode);
        //deserialize parameters that passed in
        BusinessDataVo vo = formFactory.dataFormConversion(params, formCode);
		BpmnConf bpmnConf = bmpnConfService.lambdaQuery().eq(BpmnConf::getFormCode, vo.getFormCode()).one();
		if (Objects.nonNull(vo.getFormCode()) && Objects.nonNull(bpmnConf)) {
			vo.setTemplateId(bpmnConf.getId());
			vo.setTemplateGroupId(bpmnConf.getTemplateGroupId());
			vo.setSubmitUserFieldId(bpmnConf.getSubmitUserFieldId());
		}

	    //To determine the operation Type
        ProcessOperationEnum poEnum = ProcessOperationEnum.getEnumByCode(vo.getOperationType());

        if (ObjectUtils.isEmpty(poEnum)) {
            throw new JiMuBizException("unknown operation type,please Contact the Administrator");
        }
        formCode=vo.getFormCode();
        ThreadLocalContainer.set(StringConstants.FORM_CODE,formCode);
        //set the operation Flag
        if (poEnum.getCode().equals(BUTTON_TYPE_DIS_AGREE.getCode()) || poEnum.getCode().equals(BUTTON_TYPE_STOP.getCode())) {
            vo.setFlag(false);
        } else if (poEnum.getCode().equals(BUTTON_TYPE_ABANDON.getCode())) {
            vo.setFlag(true);
        }
        //set start user Info
	    String userId = SecurityUtils.getEmpIdJwt(request);
        if (ObjectUtils.isEmpty(vo.getStartUserId())) {
	        vo.setStartUserId(userId);
            vo.setStartUserName(Optional.ofNullable(userMapper.getEmployeeDetailById(userId)).map(Employee::getUsername).orElse(null));
        }

		NodeHeadActionDto action = processService.getNodeIdApproverIdByProcessNumber(vo.getProcessNumber());
		if(Objects.isNull(action) && StringUtils.isNotBlank(vo.getProcessNumber())) {
			log.warn("DoButtonsPreOperation获取节点ID为空，流程编号{}", vo.getProcessNumber());
		}
		if (Objects.nonNull(action)) {

			String nodeIds = stringRedisTemplate.opsForValue().get(vo.getProcessNumber());
			if (StringUtils.isEmpty(nodeIds)) {
				log.warn("DoButtonsPreOperation获取节点ID列表为空，流程编号{}", vo.getProcessNumber());
			} else if (!Objects.equals(poEnum.getCode(), BUTTON_TYPE_ZB.getCode())) { // 转办和加批操作，先不要给下面的抄送人发消息。A加批给B，B操作完回到A，然后A操作完才能给下面的抄送人发消息

				BpmBusinessProcess curProcess = bpmBusinessProcessService.getProcessByProcessNumber(vo.getProcessNumber());
				String[] nodes = nodeIds.split(",");

				String nodeId = action.getNodeId().toString();
				boolean isFind = false;
				for (int i = 1; i < nodes.length; i++) {
					if (nodeId.equals(nodes[i])) {
						isFind = true;
						continue;
					}
					if (isFind) {
						String nextCopyNodeId = nodes[i];
						LambdaQueryWrapper<BpmProcessForward> qryWrapper = Wrappers.<BpmProcessForward>lambdaQuery()
								.eq(BpmProcessForward::getProcessNumber, vo.getProcessNumber())
								.eq(BpmProcessForward::getNodeId, nextCopyNodeId);
						BpmProcessForward processForward = new BpmProcessForward();
						processForward.setIsDel(0);//recover the default state,so that the forward record can be visible
						bpmProcessForwardService.update(processForward, qryWrapper);
						List<BpmProcessForward> forwards = bpmProcessForwardService.lambdaQuery()
								.eq(BpmProcessForward::getProcessNumber, vo.getProcessNumber())
								.eq(BpmProcessForward::getNodeId, nextCopyNodeId).list();
						if (!CollectionUtils.isEmpty(forwards)) {
							List<String> forwardUserIds = Lists.transform(forwards, BpmProcessForward::getForwardUserId);
							notifyService.notice(curProcess.getCreateUser(), formCode, vo.getProcessNumber().split("_")[1]
									, forwardUserIds, NoticeMsgEnum.PROCESS_COPY, vo.getProcessNumber(), null);
						} else {
							break;
						}
					}
				}
			}
		}

//
//	    NodeHeadActionDto action = processService.getNodeIdApproverIdByProcessNumber(vo.getProcessNumber());
//		if (Objects.nonNull(action)) {
//			BpmnNodeTo one = bpmnNodeToService.lambdaQuery().eq(BpmnNodeTo::getBpmnNodeId, action.getNodeId()).last("limit 1").one();
//			if (Objects.nonNull(one)) {
//				BpmnNode currCcSelftSelectNode = bpmnNodeService.lambdaQuery().eq(BpmnNode::getConfId, vo.getTemplateId()).eq(BpmnNode::getNodeId, one.getNodeTo()).eq(BpmnNode::getCcSelfSelectFlag, 1).one();
//				if (Objects.nonNull(currCcSelftSelectNode)) {
//					List<BpmProcessForward> forwards = bpmProcessForwardService.lambdaQuery()
//						.eq(BpmProcessForward::getProcessNumber, vo.getProcessNumber())
//						.eq(BpmProcessForward::getNodeId, currCcSelftSelectNode.getId()).list();
//					BpmProcessForward lastCcSelfNode = bpmProcessForwardService.lambdaQuery().select(BpmProcessForward::getNodeId)
//								.eq(BpmProcessForward::getProcessNumber, vo.getProcessNumber())
//								.isNull(BpmProcessForward::getProcessInstanceId)
//								.orderByAsc(BpmProcessForward::getNodeId)
//								.last("limit 1").one();
//					if (!CollectionUtils.isEmpty(forwards) && !Objects.equals(Long.valueOf(lastCcSelfNode.getNodeId()), currCcSelftSelectNode.getId())) {
//						notifyService.notice(vo.getStartUserId(), formCode, vo.getProcessNumber().split("_")[1], Lists.transform(forwards, BpmProcessForward::getForwardUserId), NoticeMsgEnum.PROCESS_COPY);
//					}
//				}
//			}
//		}
	    BusinessDataVo businessDataVo = buttonOperationService.buttonsOperationTransactional(vo);
	    if (!poEnum.getCode().equals(BUTTON_TYPE_SUBMIT.getCode()) && !poEnum.getCode().equals(BUTTON_TYPE_UNDERTAKE.getCode())) {
		    String businessId = vo.getProcessNumber().split("_")[1];
		    BpmBusinessProcess bpmBusinessProcess = bpmBusinessProcessMapper.getByBusinessId(businessId);
		    String createUserId = bpmBusinessProcess.getCreateUser();
		    notifyService.notice(createUserId, formCode, businessId, Collections.singletonList(createUserId), NoticeMsgEnum.PROCESS_STATE_CHANGE, businessDataVo.getProcessNumber());
	    }
		if (poEnum.getCode().equals(BUTTON_TYPE_SUBMIT.getCode())) {
			log.error("[忽略此告警],{}[{}]提交了审批单，流程单号={}, 模板编号={}, 模板名称={}", SecurityUtils.getLogInEmpName(), userId, businessDataVo.getProcessNumber(), vo.getTemplateId(), bpmnConf.getBpmnName());
		}
	    Integer ehrSource = Optional.ofNullable(userMapper.getUserById(userId)).map(User::getEhrSource).orElse(null);
	    handleApproverNull(vo, ehrSource);
		return businessDataVo;
    }

	/**
	 * 下一个审批人是否有为空的情况需要处理
	 */
	public void handleApproverNull(BusinessDataVo vo, Integer ehrSource) {
		Set<String> processedUserIds = new HashSet<>();
		handleApproverNull(vo, ehrSource, processedUserIds);
	}


	private void handleApproverNull(BusinessDataVo vo, Integer ehrSource, Set<String> processedUserIds) {
		NodeHeadActionDto action = processService.getNodeIdApproverIdByProcessNumber(vo.getProcessNumber());
		if (action == null || action.getUserMap() == null || action.getUserMap().isEmpty()) {
			log.warn("查询审批人为空，流程号: {}, action={}", vo.getProcessNumber(), JSON.toJSONString(action));
			return;
		}
		Map<String, String> userMap = action.getUserMap();
		List<BaseIdTranStruVo> userInfos = new ArrayList<>();
		String userId = new ArrayList<>(userMap.keySet()).get(0);

		// 如果当前 userId 已处理过，说明陷入重复流程，直接终止
		if (processedUserIds.contains(userId)) {
			log.warn("userId [{}] 已处理过，终止递归", userId);
			return;
		}
		processedUserIds.add(userId); // 标记已处理

		// 审批人不存在
		if (userMap.keySet().stream().anyMatch(uid -> this.userMapper.userExist(uid) < 1)) {
			log.warn("审批人[{}]为空时的处理策略NodeNoHeaderActionEnum...", JSON.toJSONString(userInfos));
			Integer noHeaderAction = action.getNoHeaderAction();
			if (Objects.equals(noHeaderAction, NodeNoHeaderActionEnum.PASS.getCode())) {
				vo.setNextNodeUserId(userId);
				vo.setTaskId(action.getTaskId());
				vo.setNextNodeUserName(NEXT_NODE_DISPLAY_NAME);
				if (action.getNodeId() != null) {
					bpmnNodeService.lambdaUpdate()
						.eq(BpmnNode::getId, action.getNodeId())
						.set(BpmnNode::getNodeDisplayName, NEXT_NODE_DISPLAY_NAME)
						.update();
				}
				passApprove(vo);
			} else if (Objects.equals(noHeaderAction, NodeNoHeaderActionEnum.TRANSFER_TO_ADMIN.getCode())) {
				userInfos.add(BaseIdTranStruVo.builder().id(userId).name(userMap.get(userId)).build());
				vo.setUserInfos(userInfos);
				transferToAdmin(vo, ehrSource);
			}
		} else {
			log.info("审批人正常，无需处理");
			return;
		}

		// 再次处理下一节点，但避免重复 userId
		handleApproverNull(vo, ehrSource, processedUserIds);
	}

	/**
	 * 审核人为空，自动审核通过
	 */
	public void passApprove(BusinessDataVo vo) {
		log.info("审核人为空，自动审核通过: {}", JSON.toJSONString(vo));
		resubmitProcessImpl.doProcessButton(vo);
	}

	/**
	 * 转交给管理员处理. 跟转办逻辑相同
	 * @param vo doButton
	 */
	public void transferToAdmin(BusinessDataVo vo, Integer ehrSource) {
		BpmBusinessProcess bpmBusinessProcess = bpmBusinessProcessService.getBpmBusinessProcess(vo.getProcessNumber());
		List<Task> list = taskService.createTaskQuery().processInstanceId(bpmBusinessProcess.getProcInstId()).list();
		//传入的用户信息List中第一个是原办理人信息,第二个是将要转的办理人信息
		List<BaseIdTranStruVo> userInfos = vo.getUserInfos();
		String originalUserId=userInfos.get(0).getId();
		String originalUserName=userInfos.get(0).getName();
		// User defaultNodeHeaderActionUser = userMapper.getDefaultNodeHeaderActionUser(ehrSource);
		// 倪瑕
		User defaultNodeHeaderActionUser = userMapper.getUserById("jt36416");
		if (defaultNodeHeaderActionUser == null) {
			throw new JiMuBizException("审核人为空，转交给管理员处理，转办人员配置错误, 无法转办!");
		}
		String transferToUserId = defaultNodeHeaderActionUser.getId();
		String transferToUserName = defaultNodeHeaderActionUser.getUserName();
		boolean matched = false;
		List<String> assignees = list.stream().map(TaskInfo::getAssignee).collect(Collectors.toList());
		int originAssigneeIndex = assignees.indexOf(originalUserId);
		if (originAssigneeIndex < 0) {
			log.warn("not matched tasks, end...");
			return;
			// throw new JiMuBizException("流程状态已变更,无当前办理人信息,转办失败!");
		}
		for (Task task : list) {
			String assignee = task.getAssignee();
			if (assignee.equals(originalUserId)) {
				bpmFlowrunEntrustService.addFlowrunEntrust(transferToUserId, transferToUserName, originalUserId, originalUserName, task.getId(), 0, bpmBusinessProcess.getProcInstId(), vo.getProcessKey());
				taskMgmtMapper.updateaActinst(
					TaskMgmtVO.builder().applyUser(transferToUserId).applyUserName(transferToUserName).taskId(task.getId()).build());
				taskMgmtMapper.updateaTaskinst(TaskMgmtVO.builder().applyUser(transferToUserId).applyUserName(transferToUserName).taskId(task.getId()).build());
				taskMgmtMapper.updateTask(TaskMgmtVO.builder().applyUser(transferToUserId).applyUserName(transferToUserName).taskId(task.getId()).build());
				matched=true;
			}
		}
		if (!matched) {
			log.warn("not matched tasks, end...");
			// throw new JiMuBizException("流程状态已变更,无当前办理人信息,转办失败!");
		}
	}
}
