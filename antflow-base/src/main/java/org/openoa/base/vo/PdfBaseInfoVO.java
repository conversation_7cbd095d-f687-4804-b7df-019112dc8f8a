package org.openoa.base.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PdfBaseInfoVO implements Serializable {

	/**
	 * 申请人（员工id）
	 */
	private String empId;

    /**
     * 申请人（员工名字）
     */
    private String empName;

    /**
     * 部门名称
     */
    private String orgName;

	/**
	 * 申请人提交时间
	 */
	private Date applyTime;

	/**
	 * 当前审批状态名称
	 */
	private String approvalStatusName;

	/**
	 * 当前状态
	 */
	private Integer approvalStatus;

	/**
	 * t_user.ehrSource 员工表 ehr来源
	 */
	private Integer ehrSource;

	/**
	 * 公司主体名称
	 */
	private String companyName;

	/**
	 * 模板名称
	 */
	private String bpmnName;

	/**
	 * 模板id
	 */
	private Long bpmnConfId;
}
