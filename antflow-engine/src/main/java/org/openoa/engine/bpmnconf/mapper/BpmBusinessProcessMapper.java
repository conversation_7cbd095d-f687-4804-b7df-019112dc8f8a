package org.openoa.engine.bpmnconf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.openoa.base.dto.wecom.NodeHeadActionDto;
import org.openoa.base.dto.wecom.NodeHeadActionRunTaskDto;
import org.openoa.base.entity.BpmBusinessProcess;

import java.util.List;

@Mapper
public interface BpmBusinessProcessMapper extends BaseMapper<BpmBusinessProcess> {

    public BpmBusinessProcess findBpmBusinessProcess(BpmBusinessProcess bpmBusinessProcess);

    /***
     * update bpm business process
     */
    public Integer updateBpmBusinessProcess(@Param("description") String description, @Param("entryId") String entryId, @Param("processState") Integer processState);

    /**
     * delete business process
     */
    public Integer delteBusinessProcess(@Param("businessKey") String businessKey);


    /**
     * Existence or not
     */
    Integer isExist(BpmBusinessProcess bpmBusinessProcess);

    BpmBusinessProcess getByBusinessId(String businessId);


	/**
	 * 获取流程节点审批人信息
	 */
	NodeHeadActionRunTaskDto getActRunTaskInfoByProcessNumber(@Param("processNumber") String processNumber);

	/**
	 * 获取转交给管理员处理的节点信息
	 */
	NodeHeadActionDto getHeadAction(@Param("processNumber") String processNumber, @Param("taskDefKey") String taskDefKey);
	NodeHeadActionDto getHeadActionSingle(@Param("processNumber") String processNumber, @Param("taskDefKey") String taskDefKey);

	/**
	 * 获取流程编号的命中流程中的所有审核人列表
	 * @param processNumber 流程编号
	 * @return t_bpmn_node.id
	 */
	Long getNodeIdsByProcessNumber(@Param("processNumber") String processNumber, @Param("procInstId") String procInstId, @Param("assignee") String assignee, @Param("assigneeName") String assigneeName);

	/**
	 * 修改流程为已拒绝并终结流程
	 */
	void makeProcessEnd(@Param("businessIdList") List<String> businessIdList);

	void updateMergedUserInfoHistory(@Param("userIds") List<String> historyUserIds, @Param("targetUserId") String targetUserId);
	void updateMergedUserInfoRuTask(@Param("userIds") List<String> historyUserIds, @Param("targetUserId") String targetUserId);
	void updateMergedUserInfoHiTask(@Param("userIds") List<String> historyUserIds, @Param("targetUserId") String targetUserId);
	void updateMergedUserInfoForward(@Param("userIds") List<String> historyUserIds, @Param("targetUserId") String targetUserId);

    BpmBusinessProcess getProcessByProcessNumber(@Param("processNumber") String processNumber);
}
